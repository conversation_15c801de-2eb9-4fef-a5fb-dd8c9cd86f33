// Supabase Edge Function for sending notification emails
import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'
import { corsHeaders } from '../_shared/cors.ts'
import { generateConnectionRequestEmail } from './email-templates/connection-request.ts'
import { generateConnectionAcceptedEmail } from './email-templates/connection-accepted.ts'
import { generatePostLikeEmail } from './email-templates/post-like.ts'
import { generatePostCommentEmail } from './email-templates/post-comment.ts'
import { generateNewMessageEmail } from './email-templates/new-message.ts'
import { generateCommunityDigestEmail } from './email-templates/community-digest.ts'
import { generateMentorshipRequestEmail } from './email-templates/mentorship-request.ts'
import { generateMentorshipRequestResponseEmail } from './email-templates/mentorship-request-response.ts'
import { generateMentorshipSessionReminderEmail } from './email-templates/mentorship-session-reminder.ts'
import { generateMentorshipEventAnnouncementEmail } from './email-templates/mentorship-event-announcement.ts'

// Environment variables
const RESEND_API_KEY = Deno.env.get('EMAIL_NOTIFICATION')
const DEFAULT_FROM_EMAIL = Deno.env.get('SENDGRID_FROM_EMAIL') || '<EMAIL>'
const DEFAULT_FROM_NAME = Deno.env.get('SENDGRID_FROM_NAME') || 'ZB Innovation Hub'

// Types for email data
interface EmailData {
  to: string
  subject: string
  html: string
  text?: string
  from?: {
    email: string
    name: string
  }
}

// Types for notification email request
interface NotificationEmailRequest {
  type: 'connection_request' | 'connection_accepted' | 'post_like' | 'post_comment' | 'new_message' | 'community_digest'
  data: {
    to: string
    firstName?: string
    lastName?: string
    // Connection-specific data
    requesterName?: string
    requesterProfileUrl?: string
    // Post-specific data
    postTitle?: string
    postUrl?: string
    likerName?: string
    commenterName?: string
    commentText?: string
    // Message-specific data
    senderName?: string
    messagePreview?: string
    conversationUrl?: string
    // Community digest data
    digestData?: {
      newPosts: number
      newMembers: number
      topPosts: Array<{ title: string; url: string; likes: number }>
      featuredContent: Array<{ title: string; url: string; type: string }>
    }
  }
}

// Function to send email via Resend API
async function sendEmail(emailData: EmailData): Promise<any> {
  if (!RESEND_API_KEY) {
    console.error('❌ Resend API key (EMAIL_NOTIFICATION) is not configured')
    throw new Error('Resend API key is not configured')
  }

  console.log('📧 Sending email with Resend:', {
    to: emailData.to,
    subject: emailData.subject,
    from: emailData.from || {
      email: DEFAULT_FROM_EMAIL,
      name: DEFAULT_FROM_NAME
    }
  })

  // Prepare the payload following Resend API documentation
  const payload = {
    from: `${emailData.from?.name || DEFAULT_FROM_NAME} <${emailData.from?.email || DEFAULT_FROM_EMAIL}>`,
    to: [emailData.to],
    subject: emailData.subject,
    html: emailData.html,
    text: emailData.text || stripHtml(emailData.html)
  }

  console.log('📤 Resend request payload:', JSON.stringify({
    ...payload,
    html: payload.html.length > 100 ? `${payload.html.substring(0, 100)}... (truncated)` : payload.html,
    text: payload.text && payload.text.length > 100 ? `${payload.text.substring(0, 100)}... (truncated)` : payload.text
  }, null, 2))

  console.log('🔑 Using API key (first 10 chars):', RESEND_API_KEY?.substring(0, 10) + '...')
  console.log('🔑 API key length:', RESEND_API_KEY?.length)

  try {
    const response = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${RESEND_API_KEY}`
      },
      body: JSON.stringify(payload)
    })

    console.log('📨 Resend API response status:', response.status)
    console.log('📨 Resend API response headers:', Object.fromEntries(response.headers.entries()))

    // Always try to get the response body for debugging
    const responseText = await response.text()
    console.log('📨 Raw response body:', responseText)

    let responseData
    try {
      responseData = JSON.parse(responseText)
    } catch (e) {
      console.error('❌ Could not parse response as JSON:', responseText)
      responseData = { rawResponse: responseText }
    }

    if (!response.ok) {
      console.error('❌ Resend API error:', {
        status: response.status,
        statusText: response.statusText,
        data: responseData
      })

      const errorMessage = `Failed to send email: ${response.status} ${response.statusText} - ${JSON.stringify(responseData)}`
      throw new Error(errorMessage)
    }

    console.log('✅ Email sent successfully!')
    console.log('📧 Response data:', responseData)
    return responseData

  } catch (error) {
    console.error('❌ Network or other error:', error)
    throw error
  }
}

// Function to strip HTML tags for plain text
function stripHtml(html: string): string {
  return html
    .replace(/<[^>]*>/g, '')
    .replace(/&nbsp;/g, ' ')
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .replace(/&#39;/g, "'")
    .replace(/\s+/g, ' ')
    .trim()
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { type, data }: NotificationEmailRequest = await req.json()

    console.log('Notification email request:', { type, to: data.to })

    let emailTemplate: { html: string; subject: string }

    // Generate email template based on type
    switch (type) {
      case 'connection_request':
        emailTemplate = generateConnectionRequestEmail(
          data.to,
          data.requesterName || 'Someone',
          data.requesterProfileUrl || '#',
          data.firstName
        )
        break

      case 'connection_accepted':
        emailTemplate = generateConnectionAcceptedEmail(
          data.to,
          data.requesterName || 'Someone',
          data.requesterProfileUrl || '#',
          data.firstName
        )
        break

      case 'post_like':
        emailTemplate = generatePostLikeEmail(
          data.to,
          data.likerName || 'Someone',
          data.postTitle || 'your post',
          data.postUrl || '#',
          data.firstName
        )
        break

      case 'post_comment':
        emailTemplate = generatePostCommentEmail(
          data.to,
          data.commenterName || 'Someone',
          data.postTitle || 'your post',
          data.commentText || '',
          data.postUrl || '#',
          data.firstName
        )
        break

      case 'new_message':
        emailTemplate = generateNewMessageEmail(
          data.to,
          data.senderName || 'Someone',
          data.messagePreview || '',
          data.conversationUrl || '#',
          data.firstName
        )
        break

      case 'community_digest':
        emailTemplate = generateCommunityDigestEmail(
          data.to,
          data.digestData || { newPosts: 0, newMembers: 0, topPosts: [], featuredContent: [] },
          data.firstName
        )
        break

      case 'mentorship_request':
        emailTemplate = generateMentorshipRequestEmail(
          data.to,
          data.menteeName || 'Someone',
          data.requestTitle || 'Mentorship Request',
          data.requestMessage || '',
          data.requestUrl || '#',
          data.firstName
        )
        break

      case 'mentorship_request_response':
        emailTemplate = generateMentorshipRequestResponseEmail(
          data.to,
          data.mentorName || 'A mentor',
          data.requestTitle || 'Your mentorship request',
          data.responseStatus || 'accepted',
          data.mentorResponse || '',
          data.dashboardUrl || '#',
          data.firstName
        )
        break

      case 'mentorship_session_reminder':
        emailTemplate = generateMentorshipSessionReminderEmail(
          data.to,
          data.participantName || 'You',
          data.otherParticipantName || 'Your mentor/mentee',
          data.sessionTitle || 'Mentorship Session',
          data.sessionDate || 'Tomorrow',
          data.sessionTime || 'TBD',
          data.meetingLink || '',
          data.meetingPlatform || 'video-call',
          data.sessionUrl || '#',
          data.firstName
        )
        break

      case 'mentorship_event_announcement':
        emailTemplate = generateMentorshipEventAnnouncementEmail(
          data.to,
          data.mentorName || 'A mentor',
          data.eventTitle || 'Mentorship Event',
          data.eventDescription || '',
          data.eventDate || 'TBD',
          data.eventTime || 'TBD',
          data.eventUrl || '#',
          data.maxParticipants || 20,
          data.currentParticipants || 0,
          data.firstName
        )
        break

      default:
        throw new Error(`Unknown notification email type: ${type}`)
    }

    // Send the email
    await sendEmail({
      to: data.to,
      subject: emailTemplate.subject,
      html: emailTemplate.html
    })

    console.log(`Notification email sent successfully to ${data.to}`)

    return new Response(
      JSON.stringify({ success: true, message: 'Notification email sent successfully' }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200
      }
    )

  } catch (error: any) {
    console.error('Error sending notification email:', error)
    return new Response(
      JSON.stringify({ success: false, error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500
      }
    )
  }
})
