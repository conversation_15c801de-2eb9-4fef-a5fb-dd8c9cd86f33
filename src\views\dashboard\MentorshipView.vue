<template>
  <q-page class="q-pa-md">
    <div class="row q-col-gutter-md">
      <!-- Hero Section with Overflow Card -->
      <div class="col-12">
        <div class="dashboard-hero-container">
          <!-- Background Hero Image -->
          <div class="hero-background"></div>

          <!-- Overlapping Welcome Card -->
          <div class="hero-card-container">
            <div class="row justify-center">
              <div class="col-12 col-md-10 col-lg-8">
                <q-card class="welcome-card-overflow">
                  <q-card-section class="q-pa-lg">
                    <!-- Welcome Message -->
                    <div class="text-h4 text-weight-light q-mb-sm text-center" style="color: #0D8A3E">
                      {{ isMentor ? 'Mentor Dashboard' : 'Mentorship Journey' }}
                    </div>
                    <div class="text-subtitle1 q-mb-md text-center">
                      {{ isMentor
                        ? 'Manage your mentorship activities, sessions, and help mentees grow their innovation journey.'
                        : 'Connect with experienced mentors to accelerate your innovation journey and achieve your goals.'
                      }}
                    </div>

                    <!-- Action Buttons -->
                    <div class="action-buttons text-center">
                      <div class="row justify-center q-gutter-sm">
                        <div class="col-auto">
                          <q-btn
                            v-if="isMentor"
                            color="primary"
                            label="CREATE EVENT"
                            icon="add"
                            size="md"
                            class="cta-button primary-btn"
                            unelevated
                            @click="showCreateEventDialog = true"
                          />
                          <q-btn
                            v-else
                            color="primary"
                            label="Find Mentors"
                            icon="search"
                            size="md"
                            class="cta-button primary-btn"
                            unelevated
                            to="/virtual-community?tab=profiles&filter=mentor"
                          />
                        </div>
                        <div class="col-auto">
                          <q-btn
                            color="green"
                            :label="isMentor ? 'FIND MENTEES' : 'Explore Community'"
                            :icon="isMentor ? 'group' : 'explore'"
                            size="md"
                            class="cta-button secondary-btn"
                            unelevated
                            :to="isMentor ? '/virtual-community?tab=profiles&filter=innovator,academic_student' : '/virtual-community'"
                          />
                        </div>
                      </div>
                    </div>
                  </q-card-section>
                </q-card>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Stats Overview -->
      <div class="col-12">
        <div class="row q-col-gutter-md">
          <div class="col-12 col-sm-6 col-md-3">
            <q-card class="dashboard-widget stat-card" flat bordered>
              <q-card-section class="text-center q-pa-lg">
                <div class="stat-icon-wrapper q-mb-md">
                  <q-icon name="pending" size="2.5rem" color="orange" />
                </div>
                <div class="text-h4 text-weight-bold q-mb-xs">{{ isMentor ? mentorshipStore.pendingReceivedRequests.length : mentorshipStore.allSentRequests.length }}</div>
                <div class="text-body2 text-grey-7">{{ isMentor ? 'Pending Requests' : 'Sent Requests' }}</div>
              </q-card-section>
            </q-card>
          </div>
          <div class="col-12 col-sm-6 col-md-3">
            <q-card class="dashboard-widget stat-card" flat bordered>
              <q-card-section class="text-center q-pa-lg">
                <div class="stat-icon-wrapper q-mb-md">
                  <q-icon name="check_circle" size="2.5rem" color="green" />
                </div>
                <div class="text-h4 text-weight-bold q-mb-xs">{{ isMentor ? mentorshipStore.acceptedReceivedRequests.length : mentorshipStore.acceptedSentRequests.length }}</div>
                <div class="text-body2 text-grey-7">Active Mentorships</div>
              </q-card-section>
            </q-card>
          </div>
          <div class="col-12 col-sm-6 col-md-3">
            <q-card class="dashboard-widget stat-card" flat bordered>
              <q-card-section class="text-center q-pa-lg">
                <div class="stat-icon-wrapper q-mb-md">
                  <q-icon name="event" size="2.5rem" color="primary" />
                </div>
                <div class="text-h4 text-weight-bold q-mb-xs">{{ mentorshipStore.upcomingSessions.length }}</div>
                <div class="text-body2 text-grey-7">Upcoming Sessions</div>
              </q-card-section>
            </q-card>
          </div>
          <div class="col-12 col-sm-6 col-md-3">
            <q-card class="dashboard-widget stat-card" flat bordered>
              <q-card-section class="text-center q-pa-lg">
                <div class="stat-icon-wrapper q-mb-md">
                  <q-icon name="today" size="2.5rem" color="primary" />
                </div>
                <div class="text-h4 text-weight-bold q-mb-xs">{{ mentorshipStore.todaysSessions.length }}</div>
                <div class="text-body2 text-grey-7">Today's Sessions</div>
              </q-card-section>
            </q-card>
          </div>
        </div>
      </div>

      <!-- Main Content -->
      <div class="col-12">
        <div class="row q-col-gutter-md">
          <!-- Left Column - Requests -->
          <div class="col-12 col-lg-6">
            <q-card class="dashboard-widget" flat bordered>
              <q-card-section class="q-pa-lg">
                <div class="section-header q-mb-lg">
                  <div class="row items-center">
                    <div class="col">
                      <h3 class="text-h6 text-weight-medium q-ma-none">
                        <q-icon name="inbox" class="q-mr-sm text-primary" />
                        Mentorship Requests
                      </h3>
                    </div>
                  </div>
                </div>
                
                <q-tabs
                  v-model="requestTab"
                  class="text-grey-7"
                  active-color="primary"
                  indicator-color="primary"
                  align="left"
                >
                  <!-- For Mentors: Received vs Accepted -->
                  <template v-if="isMentor">
                    <q-tab name="received" label="Received" />
                    <q-tab name="accepted" label="Accepted" />
                  </template>
                  <!-- For Mentees: Sent vs Accepted -->
                  <template v-else>
                    <q-tab name="sent" label="Sent" />
                    <q-tab name="accepted" label="Accepted" />
                  </template>
                </q-tabs>

                <q-separator class="q-my-md" />

                <q-tab-panels v-model="requestTab" animated>
                  <!-- MENTOR VIEW: Received Requests Tab -->
                  <q-tab-panel v-if="isMentor" name="received" class="q-pa-none">
                    <div v-if="mentorshipStore.pendingReceivedRequests.length === 0" class="empty-state text-center q-pa-xl">
                      <q-icon name="inbox" size="4rem" color="grey-4" />
                      <div class="text-h6 q-mt-md text-grey-6">No pending requests</div>
                      <div class="text-body2 text-grey-5 q-mt-xs">Mentorship requests from mentees will appear here</div>
                    </div>
                    <div v-else class="requests-list q-gutter-md">
                      <q-card
                        v-for="request in mentorshipStore.pendingReceivedRequests"
                        :key="request.id"
                        flat
                        bordered
                        class="request-card"
                      >
                        <q-card-section>
                          <div class="row items-center justify-between">
                            <div class="col">
                              <div class="text-h6">{{ request.title }}</div>
                              <div class="text-caption text-grey-6">{{ formatDate(request.created_at) }}</div>
                            </div>
                            <q-chip :color="getStatusColor(request.status)" text-color="white" size="sm">
                              {{ request.status }}
                            </q-chip>
                          </div>
                          <div class="q-mt-md">
                            <div class="text-body2">{{ request.description }}</div>
                          </div>
                          <div class="row q-mt-md q-gutter-sm">
                            <q-btn
                              @click="acceptRequest(request.id)"
                              color="positive"
                              size="sm"
                              :loading="loading"
                            >
                              Accept
                            </q-btn>
                            <q-btn
                              @click="declineRequest(request.id)"
                              color="negative"
                              size="sm"
                              outline
                              :loading="loading"
                            >
                              Decline
                            </q-btn>
                          </div>
                        </q-card-section>
                      </q-card>
                    </div>
                  </q-tab-panel>

                  <!-- MENTEE VIEW: Sent Requests Tab -->
                  <q-tab-panel v-if="!isMentor" name="sent" class="q-pa-none">
                    <div v-if="mentorshipStore.allSentRequests.length === 0" class="empty-state text-center q-pa-xl">
                      <q-icon name="send" size="4rem" color="grey-4" />
                      <div class="text-h6 q-mt-md text-grey-6">No requests sent</div>
                      <div class="text-body2 text-grey-5 q-mt-xs">Your sent requests will appear here</div>
                    </div>
                    <div v-else class="requests-list q-gutter-md">
                      <q-card
                        v-for="request in mentorshipStore.allSentRequests"
                        :key="request.id"
                        flat
                        bordered
                        class="request-card"
                      >
                        <q-card-section>
                          <div class="row items-center justify-between">
                            <div class="col">
                              <div class="text-h6">{{ request.title }}</div>
                              <div class="text-caption text-grey-6">{{ formatDate(request.created_at) }}</div>
                            </div>
                            <q-chip :color="getStatusColor(request.status)" text-color="white" size="sm">
                              {{ request.status }}
                            </q-chip>
                          </div>
                          <div class="q-mt-md">
                            <div class="text-body2">{{ request.message }}</div>
                            <!-- Show mentor response if available -->
                            <div v-if="request.mentor_response" class="q-mt-sm q-pa-sm bg-grey-1 rounded-borders">
                              <div class="text-caption text-grey-6">Mentor Response:</div>
                              <div class="text-body2 text-italic">{{ request.mentor_response }}</div>
                            </div>
                          </div>
                          <!-- MENTEE ACTIONS: Only view details, NO accept/decline -->
                          <div v-if="request.status === 'accepted'" class="row q-mt-md q-gutter-sm">
                            <q-btn
                              @click="viewMentorshipDetails(request)"
                              color="primary"
                              size="sm"
                              outline
                              icon="visibility"
                            >
                              View Details
                            </q-btn>
                            <!-- NO SCHEDULE SESSION BUTTON FOR MENTEES -->
                          </div>
                          <div v-else-if="request.status === 'pending'" class="row q-mt-md">
                            <q-btn
                              color="grey-7"
                              size="sm"
                              outline
                              icon="schedule"
                              disable
                            >
                              Awaiting Response
                            </q-btn>
                          </div>
                        </q-card-section>
                      </q-card>
                    </div>
                  </q-tab-panel>

                  <!-- BOTH VIEWS: Accepted Requests Tab -->
                  <q-tab-panel name="accepted" class="q-pa-none">
                    <div v-if="(isMentor ? mentorshipStore.acceptedReceivedRequests : mentorshipStore.acceptedSentRequests).length === 0" class="empty-state text-center q-pa-xl">
                      <q-icon name="check_circle" size="4rem" color="grey-4" />
                      <div class="text-h6 q-mt-md text-grey-6">No accepted requests</div>
                      <div class="text-body2 text-grey-5 q-mt-xs">
                        {{ isMentor ? 'Mentorship requests you accepted will appear here' : 'Your accepted mentorship requests will appear here' }}
                      </div>
                    </div>
                    <div v-else class="requests-list q-gutter-md">
                      <q-card
                        v-for="request in (isMentor ? mentorshipStore.acceptedReceivedRequests : mentorshipStore.acceptedSentRequests)"
                        :key="request.id"
                        flat
                        bordered
                        class="request-card"
                      >
                        <q-card-section>
                          <div class="row items-center justify-between">
                            <div class="col">
                              <div class="text-h6">{{ request.title }}</div>
                              <div class="text-caption text-grey-6">{{ formatDate(request.created_at) }}</div>
                            </div>
                            <q-chip color="positive" text-color="white" size="sm">
                              {{ request.status }}
                            </q-chip>
                          </div>
                          <div class="q-mt-md">
                            <div class="text-body2">{{ request.description }}</div>
                            <div v-if="request.mentor_response" class="q-mt-sm">
                              <div class="text-caption text-grey-6">Mentor Response:</div>
                              <div class="text-body2 text-italic">{{ request.mentor_response }}</div>
                            </div>
                          </div>
                          <!-- ROLE-BASED ACTIONS FOR ACCEPTED REQUESTS -->
                          <div class="row q-mt-md q-gutter-sm">
                            <!-- MENTORS: Can schedule sessions -->
                            <q-btn
                              v-if="isMentor"
                              @click="scheduleSession(request)"
                              color="primary"
                              size="sm"
                              icon="event"
                            >
                              Schedule Session
                            </q-btn>
                            <!-- BOTH: Can view details -->
                            <q-btn
                              @click="viewMentorshipDetails(request)"
                              color="grey-7"
                              size="sm"
                              outline
                              icon="visibility"
                            >
                              View Details
                            </q-btn>
                            <!-- MENTEES: Show contact mentor option instead of schedule -->
                            <q-btn
                              v-if="!isMentor"
                              @click="contactMentor(request)"
                              color="positive"
                              size="sm"
                              outline
                              icon="message"
                            >
                              Contact Mentor
                            </q-btn>
                          </div>
                        </q-card-section>
                      </q-card>
                    </div>
                  </q-tab-panel>
                </q-tab-panels>
              </q-card-section>
            </q-card>
          </div>

          <!-- Right Column - Sessions -->
          <div class="col-12 col-lg-6">
            <q-card class="dashboard-widget" flat bordered>
              <q-card-section class="q-pa-lg">
                <div class="section-header q-mb-lg">
                  <div class="row items-center">
                    <div class="col">
                      <h3 class="text-h6 text-weight-medium q-ma-none">
                        <q-icon name="event" class="q-mr-sm text-primary" />
                        {{ isMentor ? 'Your Sessions & Events' : 'Upcoming Sessions' }}
                      </h3>
                    </div>
                    <div v-if="isMentor" class="col-auto">
                      <q-btn
                        color="primary"
                        icon="add"
                        label="Create Event"
                        size="sm"
                        @click="showCreateEventDialog = true"
                      />
                    </div>
                  </div>
                </div>
                
                <!-- Tabs for Sessions and Events -->
                <q-tabs
                  v-model="sessionTab"
                  class="text-grey-7"
                  active-color="primary"
                  indicator-color="primary"
                  align="left"
                >
                  <q-tab name="sessions" label="Sessions" />
                  <q-tab name="events" label="Events" />
                </q-tabs>

                <q-separator class="q-my-md" />

                <q-tab-panels v-model="sessionTab" animated>
                  <q-tab-panel name="sessions" class="q-pa-none">
                    <div v-if="mentorshipStore.upcomingSessions.length === 0" class="empty-state text-center q-pa-xl">
                      <q-icon name="event_available" size="4rem" color="grey-4" />
                      <div class="text-h6 q-mt-md text-grey-6">No upcoming sessions</div>
                      <div class="text-body2 text-grey-5 q-mt-xs">Scheduled sessions will appear here</div>
                    </div>
                <div v-else class="sessions-list q-gutter-md">
                  <q-card
                    v-for="session in mentorshipStore.upcomingSessions"
                    :key="session.id"
                    flat
                    bordered
                    class="session-card"
                  >
                    <q-card-section class="q-pa-lg">
                      <div class="text-subtitle1 text-weight-medium q-mb-xs">{{ session.title }}</div>
                      <div class="text-body2 text-grey-7 q-mb-sm">{{ session.description }}</div>
                      <div class="row items-center q-mt-sm q-gutter-sm">
                        <q-icon name="schedule" size="sm" color="grey-6" />
                        <span class="text-caption text-grey-6">
                          {{ formatDateTime(session.scheduled_start_time || session.scheduled_start) }}
                        </span>
                      </div>
                      <div class="row items-center q-mt-xs q-gutter-sm">
                        <q-icon name="videocam" size="sm" color="grey-6" />
                        <span class="text-caption text-grey-6">{{ session.meeting_platform || session.meeting_location || 'Online' }}</span>
                      </div>
                      <div class="q-mt-md">
                        <q-btn
                          v-if="session.meeting_link"
                          color="primary"
                          label="Join Meeting"
                          size="sm"
                          :href="session.meeting_link"
                          target="_blank"
                          class="q-mr-sm"
                        />
                        <q-btn
                          color="grey-7"
                          label="Send Reminder"
                          size="sm"
                          outline
                          @click="sendReminder(session.id)"
                        />
                      </div>
                    </q-card-section>
                  </q-card>
                </div>
                  </q-tab-panel>

                  <q-tab-panel name="events" class="q-pa-none">
                    <div v-if="mentorshipStore.upcomingEvents.length === 0" class="empty-state text-center q-pa-xl">
                      <q-icon name="event" size="4rem" color="grey-4" />
                      <div class="text-h6 q-mt-md text-grey-6">No upcoming events</div>
                      <div class="text-body2 text-grey-5 q-mt-xs">Your mentorship events will appear here</div>
                    </div>
                    <div v-else class="events-list q-gutter-md">
                      <q-card
                        v-for="event in mentorshipStore.upcomingEvents"
                        :key="event.id"
                        flat
                        bordered
                        class="event-card"
                      >
                        <q-card-section class="q-pa-lg">
                          <div class="row items-start justify-between">
                            <div class="col">
                              <div class="text-subtitle1 text-weight-medium q-mb-xs">{{ event.title }}</div>
                              <div class="text-body2 text-grey-7 q-mb-sm">{{ event.description }}</div>
                              <div class="row items-center q-mt-sm q-gutter-sm">
                                <q-icon name="schedule" size="sm" color="grey-6" />
                                <span class="text-caption text-grey-6">
                                  {{ formatDateTime(event.scheduled_start_time) }}
                                </span>
                              </div>
                              <div class="row items-center q-mt-xs q-gutter-sm">
                                <q-icon name="people" size="sm" color="grey-6" />
                                <span class="text-caption text-grey-6">
                                  {{ event.current_participants || 0 }}/{{ event.max_participants }} participants
                                </span>
                              </div>
                            </div>
                            <div class="col-auto">
                              <q-chip
                                :color="event.status === 'published' ? 'green' : 'orange'"
                                text-color="white"
                                :label="event.status"
                                size="sm"
                              />
                            </div>
                          </div>
                          <div class="q-mt-md">
                            <q-btn
                              color="primary"
                              label="View Event"
                              size="sm"
                              :to="`/events/${event.id}`"
                              class="q-mr-sm"
                            />
                            <q-btn
                              v-if="isMentor"
                              color="grey"
                              label="Manage"
                              size="sm"
                              outline
                            />
                          </div>
                        </q-card-section>
                      </q-card>
                    </div>
                  </q-tab-panel>
                </q-tab-panels>
              </q-card-section>
            </q-card>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading Overlay -->
    <q-inner-loading :showing="mentorshipStore.loading">
      <q-spinner-gears size="50px" color="primary" />
    </q-inner-loading>

    <!-- Create Event Dialog -->
    <q-dialog v-model="showCreateEventDialog" persistent>
      <q-card style="min-width: 600px; max-width: 800px;">
        <q-card-section class="row items-center q-pb-none">
          <div class="text-h6">Create Mentorship Event</div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-card-section>
          <mentorship-event-form
            @success="handleEventCreated"
            @cancel="showCreateEventDialog = false"
          />
        </q-card-section>
      </q-card>
    </q-dialog>
  </q-page>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useQuasar } from 'quasar'
import { useMentorshipStore } from '@/stores/mentorship'
import { useAuthStore } from '@/stores/auth'
import { useProfileStore } from '@/stores/profileStore'


const $q = useQuasar()
const mentorshipStore = useMentorshipStore()
const authStore = useAuthStore()
const profileStore = useProfileStore()

// State
const requestTab = ref('sent')
const sessionTab = ref('sessions')
const showCreateEvent = ref(false)
const showCreateEventDialog = ref(false)

// Computed
const isMentor = computed(() => {
  return profileStore.currentProfile?.profile_type === 'mentor'
})

// Watch for profile changes and update default tab
watch(isMentor, (newValue) => {
  if (newValue) {
    requestTab.value = 'received' // Mentors start with received requests
  } else {
    requestTab.value = 'sent' // Mentees start with sent requests
  }
}, { immediate: true })

// Methods
function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString()
}

function formatDateTime(dateString: string): string {
  return new Date(dateString).toLocaleString()
}

function getStatusColor(status: string): string {
  const colors: Record<string, string> = {
    pending: 'orange',
    accepted: 'green',
    declined: 'red',
    withdrawn: 'grey',
    expired: 'grey-7'
  }
  return colors[status] || 'grey'
}

async function respondToRequest(requestId: string, status: 'accepted' | 'declined') {
  const success = await mentorshipStore.respondToRequest(requestId, status)
  if (success) {
    $q.notify({
      type: 'positive',
      message: `Request ${status} successfully!`
    })
  }
}

// Mentor-specific actions
async function acceptRequest(requestId: string) {
  try {
    loading.value = true
    await mentorshipStore.respondToRequest(requestId, 'accepted', 'I would be happy to mentor you!')
    await mentorshipStore.loadReceivedRequests()
  } catch (error) {
    console.error('Error accepting request:', error)
  } finally {
    loading.value = false
  }
}

async function declineRequest(requestId: string) {
  try {
    loading.value = true
    await mentorshipStore.respondToRequest(requestId, 'declined', 'Thank you for your interest, but I am not available at this time.')
    await mentorshipStore.loadReceivedRequests()
  } catch (error) {
    console.error('Error declining request:', error)
  } finally {
    loading.value = false
  }
}

// Common actions
function scheduleSession(request: any) {
  // TODO: Implement session scheduling
  console.log('Schedule session for request:', request.id)
}

function viewMentorshipDetails(request: any) {
  // TODO: Implement mentorship details view
  console.log('View details for request:', request.id)
}

function contactMentor(request: any) {
  // TODO: Implement mentor contact functionality
  console.log('Contact mentor for request:', request.id)
  // This could open a messaging interface or redirect to mentor's profile
}

async function sendReminder(sessionId: string) {
  const success = await mentorshipStore.sendSessionReminders(sessionId)
  if (success) {
    $q.notify({
      type: 'positive',
      message: 'Reminder sent successfully!'
    })
  }
}

async function handleEventCreated(eventData: any) {
  try {
    // Import the mentorship event service
    const { MentorshipEventService } = await import('@/services/mentorshipEventService')
    const eventService = new MentorshipEventService()

    // Add mentorship tag and mentor_id
    const mentorshipEventData = {
      ...eventData,
      mentor_id: authStore.user?.id,
      tags: [...(eventData.tags || []), 'mentorship'],
      category: 'mentorship',
      status: 'published'
    }

    const { data, error } = await eventService.createEvent(mentorshipEventData)

    if (error) {
      throw error
    }

    $q.notify({
      type: 'positive',
      message: 'Mentorship event created successfully!',
      caption: 'Your event is now live and accepting registrations.'
    })

    showCreateEventDialog.value = false

    // Refresh the mentorship store to show the new event
    await mentorshipStore.initialize()

  } catch (error: any) {
    console.error('Error creating mentorship event:', error)
    $q.notify({
      type: 'negative',
      message: 'Failed to create mentorship event',
      caption: error.message || 'Please try again later'
    })
  }
}



// Lifecycle
onMounted(async () => {
  // Ensure profile is loaded first
  if (!profileStore.currentProfile) {
    await profileStore.loadUserProfiles()
  }
  await mentorshipStore.initialize()
})
</script>

<style scoped>
/* Dashboard Hero Container */
.dashboard-hero-container {
  position: relative;
  height: 280px;
  margin-bottom: 20px; /* Reduced from 40px to 20px */
  overflow: hidden;
  border-radius: 12px;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('https://ext.same-assets.com/4258758033/1505177164.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.hero-background::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(13, 138, 62, 0.6); /* Green overlay with 60% opacity */
  z-index: 1;
}

.hero-card-container {
  position: absolute;
  bottom: -30px;
  left: 0;
  right: 0;
  z-index: 10;
}

.welcome-card-overflow {
  background: white;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

/* Action Buttons */
.action-buttons {
  margin-top: 1.5rem;
}

.cta-button {
  min-width: 160px;
  height: 48px;
  border-radius: 24px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
}

.primary-btn {
  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
  color: white;
}

.primary-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(25, 118, 210, 0.3);
}

.secondary-btn {
  background: linear-gradient(135deg, #0D8A3E 0%, #0A6B31 100%);
  color: white;
}

.secondary-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(13, 138, 62, 0.3);
}

/* Dashboard Widgets */
.dashboard-widget {
  background: white;
  border-radius: 16px;
  border: 1px solid #e0e0e0;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.dashboard-widget:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #1976d2;
}

.stat-card {
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stat-icon-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: rgba(25, 118, 210, 0.1);
  margin: 0 auto;
}

.section-header {
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 1rem;
}

.empty-state {
  background: #f8f9fa;
  border-radius: 8px;
  margin: 1rem 0;
}

.request-card,
.session-card {
  transition: all 0.2s ease;
  border-radius: 8px;
  border: 1px solid #e8eaed;
}

.request-card:hover,
.session-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #1976d2;
}

.requests-list,
.sessions-list {
  max-height: 500px;
  overflow-y: auto;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-hero-container {
    height: 220px;
    margin-bottom: 15px; /* Reduced from 30px to 15px */
  }

  .hero-card-container {
    bottom: -25px;
  }

  .cta-button {
    min-width: 140px;
    height: 44px;
  }

  .action-buttons .row {
    flex-direction: column;
    align-items: center;
  }

  .action-buttons .col-auto {
    width: 100%;
    max-width: 280px;
    margin-bottom: 0.5rem;
  }
}
</style>
