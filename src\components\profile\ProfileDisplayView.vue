<template>
  <div class="profile-display-view">
    <div v-if="loading" class="q-pa-md flex flex-center">
      <q-spinner color="primary" size="3em" />
      <div class="q-ml-md">Loading profile data...</div>
    </div>

    <div v-else-if="error" class="q-pa-md flex flex-center column">
      <q-card class="q-pa-lg text-center" style="max-width: 500px">
        <q-card-section>
          <unified-icon name="error" color="warning" size="4em" />
          <div class="text-h5 q-mt-md">Profile Setup Required</div>
          <div class="text-subtitle1 q-mt-sm">{{ error }}</div>
          <div v-if="error.includes('category')" class="text-body2 q-mt-md">
            You need to create a profile with a category before you can view it.
          </div>
        </q-card-section>
        <q-card-actions align="center" class="q-gutter-md">
          <q-btn
            v-if="error.includes('category') && isCurrentUser"
            color="primary"
            label="Create Profile"
            :to="{ name: 'profile-create' }"
            class="q-mt-md"
            icon-right="person_add"
          />
          <q-btn
            color="light-green-8"
            label="Back to Dashboard"
            to="/dashboard"
            class="q-mt-md"
            icon-right="arrow_back"
          />
        </q-card-actions>
      </q-card>
    </div>

    <!-- New User Welcome Component -->
    <div v-else-if="!baseProfile || !profileData">
      <new-user-welcome @create-profile="$emit('create-profile')" />
    </div>

    <div v-else>
      <!-- Profile Header -->
      <q-card class="profile-header-card q-mb-lg">
        <q-card-section class="bg-primary text-white profile-header-section">
          <div class="row items-center q-col-gutter-md">
            <div class="col-12 col-sm-auto text-center">
              <q-avatar size="100px" class="profile-avatar bg-white q-mb-sm-none q-mb-md">
                <img v-if="baseProfile.avatar_url" :src="baseProfile.avatar_url" alt="Profile Avatar">
                <div v-else class="text-primary flex flex-center full-height">
                  {{ getInitials(baseProfile.first_name, baseProfile.last_name) }}
                </div>
              </q-avatar>
            </div>
            <div class="col-12 col-sm profile-info">
              <div class="profile-name">{{ baseProfile.first_name }} {{ baseProfile.last_name }}</div>
              <div class="profile-type q-mt-sm">{{ formatProfileType(baseProfile.profile_type) }}</div>
              <div class="profile-email q-mt-xs">{{ baseProfile.email }}</div>
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- Profile Completion -->
      <div v-if="baseProfile.profile_completion < 100" class="profile-completion q-mb-lg">
        <div class="text-subtitle1 q-mb-sm">Profile Completion</div>
        <q-linear-progress
          :value="baseProfile.profile_completion / 100"
          color="primary"
          class="q-mb-sm"
          size="10px"
        />
        <div class="text-caption">
          {{ Math.round(baseProfile.profile_completion) }}% complete
        </div>
      </div>

      <!-- Personal Details Section -->
      <q-card class="q-mb-md section-card">
        <q-card-section class="section-header bg-primary text-white">
          <div class="text-h6">
            <unified-icon name="person" class="q-mr-sm" />
            Personal Details
          </div>
        </q-card-section>

        <q-card-section>
          <div class="row q-col-gutter-md">
            <div class="col-12 col-md-6">
              <div class="text-subtitle2">First Name</div>
              <div>{{ baseProfile.first_name || 'Not provided' }}</div>
            </div>

            <div class="col-12 col-md-6">
              <div class="text-subtitle2">Last Name</div>
              <div>{{ baseProfile.last_name || 'Not provided' }}</div>
            </div>

            <div class="col-12 col-md-6">
              <div class="text-subtitle2">Email</div>
              <div>{{ baseProfile.email || 'Not provided' }}</div>
            </div>

            <div class="col-12 col-md-6">
              <div class="text-subtitle2">Phone</div>
              <div>
                <span v-if="baseProfile.phone_country_code && baseProfile.phone_number">
                  {{ baseProfile.phone_country_code }} {{ baseProfile.phone_number }}
                </span>
                <span v-else>Not provided</span>
              </div>
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- Profile Details Section -->
      <q-card class="q-mb-md section-card">
        <q-card-section class="section-header bg-primary text-white">
          <div class="text-h6">
            <unified-icon name="badge" class="q-mr-sm" />
            Profile Details
          </div>
        </q-card-section>

        <q-card-section>
          <div class="row q-col-gutter-md">
            <!-- Profile Name -->
            <div class="col-12 col-md-6">
              <div class="text-subtitle2">Profile Name</div>
              <div>{{ profileData.profile_name || 'Not provided' }}</div>
            </div>

            <!-- Public Status -->
            <div class="col-12 col-md-6">
              <div class="text-subtitle2">Public Status</div>
              <div>
                <q-badge :color="profileData.is_public ? 'positive' : 'negative'">
                  {{ profileData.is_public ? 'Public' : 'Private' }}
                </q-badge>
              </div>
            </div>

            <!-- Website -->
            <div class="col-12 col-md-6" v-if="profileData.website !== undefined">
              <div class="text-subtitle2">Website</div>
              <div>
                <a v-if="profileData.website" :href="profileData.website" target="_blank">
                  {{ profileData.website }}
                </a>
                <span v-else>Not provided</span>
              </div>
            </div>

            <!-- Bio -->
            <div class="col-12" v-if="profileData.bio !== undefined">
              <div class="text-subtitle2">Bio</div>
              <div>{{ profileData.bio || 'Not provided' }}</div>
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- Social Media Section -->
      <q-card v-if="hasSocialMedia" class="q-mb-md section-card">
        <q-card-section class="section-header bg-primary text-white">
          <div class="text-h6">
            <unified-icon name="share" class="q-mr-sm" />
            Social Media
          </div>
        </q-card-section>

        <q-card-section>
          <div class="row q-col-gutter-md">
            <div class="col-12 col-md-6" v-if="profileData.linkedin !== undefined">
              <div class="text-subtitle2">LinkedIn</div>
              <div>
                <a v-if="profileData.linkedin" :href="profileData.linkedin" target="_blank">
                  {{ profileData.linkedin }}
                </a>
                <span v-else>Not provided</span>
              </div>
            </div>

            <div class="col-12 col-md-6" v-if="profileData.twitter !== undefined">
              <div class="text-subtitle2">Twitter</div>
              <div>
                <a v-if="profileData.twitter" :href="profileData.twitter" target="_blank">
                  {{ profileData.twitter }}
                </a>
                <span v-else>Not provided</span>
              </div>
            </div>

            <div class="col-12 col-md-6" v-if="profileData.facebook !== undefined">
              <div class="text-subtitle2">Facebook</div>
              <div>
                <a v-if="profileData.facebook" :href="profileData.facebook" target="_blank">
                  {{ profileData.facebook }}
                </a>
                <span v-else>Not provided</span>
              </div>
            </div>

            <div class="col-12 col-md-6" v-if="profileData.instagram !== undefined">
              <div class="text-subtitle2">Instagram</div>
              <div>
                <a v-if="profileData.instagram" :href="profileData.instagram" target="_blank">
                  {{ profileData.instagram }}
                </a>
                <span v-else>Not provided</span>
              </div>
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- Contact Information Section -->
      <q-card v-if="hasContactInfo" class="q-mb-md section-card">
        <q-card-section class="section-header bg-primary text-white">
          <div class="text-h6">
            <unified-icon name="contact_mail" class="q-mr-sm" />
            Contact Information
          </div>
        </q-card-section>

        <q-card-section>
          <div class="row q-col-gutter-md">
            <div class="col-12 col-md-6" v-if="profileData.contact_email !== undefined">
              <div class="text-subtitle2">Contact Email</div>
              <div>
                <a v-if="profileData.contact_email" :href="`mailto:${profileData.contact_email}`">
                  {{ profileData.contact_email }}
                </a>
                <span v-else>Not provided</span>
              </div>
            </div>

            <div class="col-12 col-md-6" v-if="profileData.contact_phone !== undefined">
              <div class="text-subtitle2">Contact Phone</div>
              <div>{{ profileData.contact_phone || 'Not provided' }}</div>
            </div>

            <div class="col-12 col-md-6" v-if="profileData.whatsapp !== undefined">
              <div class="text-subtitle2">WhatsApp</div>
              <div>{{ profileData.whatsapp || 'Not provided' }}</div>
            </div>

            <div class="col-12 col-md-6" v-if="profileData.telegram !== undefined">
              <div class="text-subtitle2">Telegram</div>
              <div>{{ profileData.telegram || 'Not provided' }}</div>
            </div>

            <div class="col-12 col-md-6" v-if="profileData.preferred_contact_method !== undefined">
              <div class="text-subtitle2">Preferred Contact Method</div>
              <div>{{ profileData.preferred_contact_method || 'Not provided' }}</div>
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- Location Section -->
      <q-card v-if="hasLocationInfo" class="q-mb-md section-card">
        <q-card-section class="section-header bg-primary text-white">
          <div class="text-h6">
            <unified-icon name="location_on" class="q-mr-sm" />
            Location
          </div>
        </q-card-section>

        <q-card-section>
          <div class="row q-col-gutter-md">
            <div class="col-12" v-if="profileData.address !== undefined">
              <div class="text-subtitle2">Address</div>
              <div>{{ profileData.address || 'Not provided' }}</div>
            </div>

            <div class="col-12 col-md-6" v-if="profileData.city !== undefined">
              <div class="text-subtitle2">City</div>
              <div>{{ profileData.city || 'Not provided' }}</div>
            </div>

            <div class="col-12 col-md-6" v-if="profileData.state_province !== undefined">
              <div class="text-subtitle2">State/Province</div>
              <div>{{ profileData.state_province || 'Not provided' }}</div>
            </div>

            <div class="col-12 col-md-6" v-if="profileData.country !== undefined">
              <div class="text-subtitle2">Country</div>
              <div>{{ profileData.country || 'Not provided' }}</div>
            </div>

            <div class="col-12 col-md-6" v-if="profileData.postal_code !== undefined">
              <div class="text-subtitle2">Postal Code</div>
              <div>{{ profileData.postal_code || 'Not provided' }}</div>
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- Professional Information Section (for relevant profile types) -->
      <q-card v-if="hasProfessionalInfo" class="q-mb-md section-card">
        <q-card-section class="section-header bg-primary text-white">
          <div class="text-h6">
            <unified-icon name="work" class="q-mr-sm" />
            Professional Information
          </div>
        </q-card-section>

        <q-card-section>
          <div class="row q-col-gutter-md">
            <div class="col-12 col-md-6" v-if="profileData.job_title !== undefined">
              <div class="text-subtitle2">Job Title</div>
              <div>{{ profileData.job_title || 'Not provided' }}</div>
            </div>

            <div class="col-12 col-md-6" v-if="profileData.company !== undefined">
              <div class="text-subtitle2">Company</div>
              <div>{{ profileData.company || 'Not provided' }}</div>
            </div>

            <div class="col-12 col-md-6" v-if="profileData.industry !== undefined">
              <div class="text-subtitle2">Industry</div>
              <div>{{ profileData.industry || 'Not provided' }}</div>
            </div>

            <div class="col-12 col-md-6" v-if="profileData.years_of_experience !== undefined">
              <div class="text-subtitle2">Years of Experience</div>
              <div>{{ profileData.years_of_experience || 'Not provided' }}</div>
            </div>

            <div class="col-12" v-if="profileData.skills !== undefined">
              <div class="text-subtitle2">Skills</div>
              <div v-if="Array.isArray(profileData.skills) && profileData.skills.length > 0">
                <q-chip v-for="skill in profileData.skills" :key="skill" color="primary" text-color="white" class="q-ma-xs">
                  {{ skill }}
                </q-chip>
              </div>
              <div v-else>Not provided</div>
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- Goals & Interests Section -->
      <q-card v-if="hasGoalsAndInterests" class="q-mb-md section-card">
        <q-card-section class="section-header bg-primary text-white">
          <div class="text-h6">
            <unified-icon name="emoji_objects" class="q-mr-sm" />
            Goals & Interests
          </div>
        </q-card-section>

        <q-card-section>
          <!-- Short Term Goals -->
          <div v-if="profileData.short_term_goals" class="q-mb-md">
            <div class="text-subtitle1 q-mb-sm">Short Term Goals</div>
            <div v-if="isJsonObject(profileData.short_term_goals)">
              <q-list dense>
                <q-item v-for="(value, key) in profileData.short_term_goals" :key="key">
                  <q-item-section>
                    <q-item-label>{{ formatFieldName(key) }}: {{ value }}</q-item-label>
                  </q-item-section>
                </q-item>
              </q-list>
            </div>
            <div v-else-if="Array.isArray(profileData.short_term_goals) && profileData.short_term_goals.length > 0">
              <q-list dense>
                <q-item v-for="(goal, index) in profileData.short_term_goals" :key="index">
                  <q-item-section>
                    <q-item-label>{{ goal }}</q-item-label>
                  </q-item-section>
                </q-item>
              </q-list>
            </div>
            <div v-else>{{ profileData.short_term_goals }}</div>
          </div>

          <!-- Long Term Goals -->
          <div v-if="profileData.long_term_goals" class="q-mb-md">
            <div class="text-subtitle1 q-mb-sm">Long Term Goals</div>
            <div v-if="isJsonObject(profileData.long_term_goals)">
              <q-list dense>
                <q-item v-for="(value, key) in profileData.long_term_goals" :key="key">
                  <q-item-section>
                    <q-item-label>{{ formatFieldName(key) }}: {{ value }}</q-item-label>
                  </q-item-section>
                </q-item>
              </q-list>
            </div>
            <div v-else-if="Array.isArray(profileData.long_term_goals) && profileData.long_term_goals.length > 0">
              <q-list dense>
                <q-item v-for="(goal, index) in profileData.long_term_goals" :key="index">
                  <q-item-section>
                    <q-item-label>{{ goal }}</q-item-label>
                  </q-item-section>
                </q-item>
              </q-list>
            </div>
            <div v-else>{{ profileData.long_term_goals }}</div>
          </div>

          <!-- Goals (Array) -->
          <div v-if="profileData.goals && Array.isArray(profileData.goals) && profileData.goals.length > 0" class="q-mb-md">
            <div class="text-subtitle1 q-mb-sm">Goals</div>
            <q-list dense>
              <q-item v-for="(goal, index) in profileData.goals" :key="index">
                <q-item-section>
                  <q-item-label>{{ goal }}</q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
          </div>

          <!-- Interests -->
          <div v-if="profileData.interests" class="q-mb-md">
            <div class="text-subtitle1 q-mb-sm">Interests</div>
            <div v-if="isJsonObject(profileData.interests)">
              <q-list dense>
                <q-item v-for="(value, key) in profileData.interests" :key="key">
                  <q-item-section>
                    <q-item-label>{{ formatFieldName(key) }}: {{ value }}</q-item-label>
                  </q-item-section>
                </q-item>
              </q-list>
            </div>
            <div v-else-if="Array.isArray(profileData.interests) && profileData.interests.length > 0">
              <q-chip v-for="interest in profileData.interests" :key="interest" color="teal" text-color="white" class="q-ma-xs">
                {{ interest }}
              </q-chip>
            </div>
            <div v-else>{{ profileData.interests }}</div>
          </div>

          <!-- Collaboration Interests -->
          <div v-if="profileData.collaboration_interests" class="q-mb-md">
            <div class="text-subtitle1 q-mb-sm">Collaboration Interests</div>
            <div v-if="isJsonObject(profileData.collaboration_interests)">
              <q-list dense>
                <q-item v-for="(value, key) in profileData.collaboration_interests" :key="key">
                  <q-item-section>
                    <q-item-label>{{ formatFieldName(key) }}: {{ value }}</q-item-label>
                  </q-item-section>
                </q-item>
              </q-list>
            </div>
            <div v-else-if="Array.isArray(profileData.collaboration_interests) && profileData.collaboration_interests.length > 0">
              <q-chip v-for="interest in profileData.collaboration_interests" :key="interest" color="purple" text-color="white" class="q-ma-xs">
                {{ interest }}
              </q-chip>
            </div>
            <div v-else>{{ profileData.collaboration_interests }}</div>
          </div>

          <!-- Additional Interests -->
          <div v-if="profileData.additional_interests" class="q-mb-md">
            <div class="text-subtitle1 q-mb-sm">Additional Interests</div>
            <div>{{ profileData.additional_interests }}</div>
          </div>
        </q-card-section>
      </q-card>

      <!-- All Available Profile Data Section (shown for current user or in debug mode) -->
      <q-card v-if="props.isCurrentUser || showDebug" class="q-mb-md section-card">
        <q-card-section class="section-header bg-primary text-white">
          <div class="text-h6">
            <unified-icon name="list_alt" class="q-mr-sm" />
            All Profile Data
          </div>
        </q-card-section>

        <q-card-section>
          <div class="row q-col-gutter-md">
            <!-- Dynamically generate fields for all available profile data -->
            <template v-for="(value, key) in profileData" :key="key">
              <div class="col-12 col-md-6" v-if="typeof value !== 'object' && key !== 'id' && key !== 'user_id' && !key.includes('_at')">
                <div class="text-subtitle2">{{ formatFieldName(key) }}</div>
                <div>{{ value || 'Not provided' }}</div>
              </div>
            </template>
          </div>
        </q-card-section>
      </q-card>

      <!-- Debug Section (only shown when showDebug is true) -->
      <q-card v-if="showDebug" class="q-mb-md section-card">
        <q-card-section class="section-header bg-grey-8 text-white">
          <div class="text-h6">
            <unified-icon name="code" class="q-mr-sm" />
            Debug Information
          </div>
        </q-card-section>

        <q-card-section>
          <div class="text-subtitle2">Profile ID</div>
          <div>{{ baseProfile.id }}</div>

          <div class="text-subtitle2 q-mt-md">User ID</div>
          <div>{{ baseProfile.user_id }}</div>

          <div class="text-subtitle2 q-mt-md">Profile Type</div>
          <div>{{ baseProfile.profile_type }}</div>

          <div class="text-subtitle2 q-mt-md">Data Source</div>
          <div>{{ source }}</div>

          <div class="text-subtitle2 q-mt-md">Raw Profile Data</div>
          <pre class="bg-grey-2 q-pa-sm">{{ JSON.stringify(profileData, null, 2) }}</pre>
        </q-card-section>
      </q-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useAuthStore } from '../../stores/auth'
import { useProfileStore } from '../../stores/profile'
import { useGlobalServicesStore } from '../../stores/globalServices'
import { supabase } from '../../lib/supabase'
import UnifiedIcon from '../ui/UnifiedIcon.vue'
import NewUserWelcome from './NewUserWelcome.vue'
import { getNameFromEmail, getInitials as getInitialsFromString } from '../../utils/nameUtils'

const props = defineProps({
  profileId: {
    type: String,
    required: true
  },
  isCurrentUser: {
    type: Boolean,
    default: false
  },
  showDebug: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['create-profile'])

// State
const loading = ref(true)
const error = ref('')
const baseProfile = ref(null)
const profileData = ref(null)
const source = ref('')

// Stores
const authStore = useAuthStore()
const profileStore = useProfileStore()
const globalServices = useGlobalServicesStore()
const profileService = globalServices.profileService

// Computed properties
const hasSocialMedia = computed(() => {
  if (!profileData.value) return false
  return (
    profileData.value.linkedin !== undefined ||
    profileData.value.twitter !== undefined ||
    profileData.value.facebook !== undefined ||
    profileData.value.instagram !== undefined
  )
})

const hasContactInfo = computed(() => {
  if (!profileData.value) return false
  return (
    profileData.value.contact_email !== undefined ||
    profileData.value.contact_phone !== undefined ||
    profileData.value.whatsapp !== undefined ||
    profileData.value.telegram !== undefined ||
    profileData.value.preferred_contact_method !== undefined
  )
})

const hasLocationInfo = computed(() => {
  if (!profileData.value) return false
  return (
    profileData.value.address !== undefined ||
    profileData.value.city !== undefined ||
    profileData.value.state_province !== undefined ||
    profileData.value.country !== undefined ||
    profileData.value.postal_code !== undefined
  )
})

const hasProfessionalInfo = computed(() => {
  if (!profileData.value) return false
  return (
    profileData.value.job_title !== undefined ||
    profileData.value.company !== undefined ||
    profileData.value.industry !== undefined ||
    profileData.value.years_of_experience !== undefined ||
    profileData.value.skills !== undefined
  )
})

const hasGoalsAndInterests = computed(() => {
  if (!profileData.value) return false
  return (
    profileData.value.short_term_goals !== undefined ||
    profileData.value.long_term_goals !== undefined ||
    profileData.value.goals !== undefined ||
    profileData.value.interests !== undefined ||
    profileData.value.collaboration_interests !== undefined ||
    profileData.value.additional_interests !== undefined
  )
})

// Methods
function getInitials(firstName, lastName) {
  // If both first and last name are empty or undefined, try to get initials from email
  if ((!firstName || firstName.trim() === '') && (!lastName || lastName.trim() === '')) {
    if (baseProfile.value && baseProfile.value.email) {
      return getInitialsFromString(baseProfile.value.email);
    }
    return 'U';
  }

  const first = firstName ? firstName.charAt(0).toUpperCase() : '';
  const last = lastName ? lastName.charAt(0).toUpperCase() : '';
  return first + last;
}

function formatProfileType(type) {
  if (!type) return 'Unknown'

  // Convert snake_case to Title Case
  return type
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ')
}

function formatFieldName(fieldName) {
  if (!fieldName) return ''

  // Convert snake_case or camelCase to Title Case with spaces
  return fieldName
    // Insert a space before all caps and uppercase the first character
    .replace(/([A-Z])/g, ' $1')
    // Replace underscores with spaces
    .replace(/_/g, ' ')
    // Uppercase first character of each word
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ')
    .trim()
}

function isJsonObject(value) {
  if (!value || typeof value !== 'object') return false

  // Check if it's an object but not an array
  return value !== null && !Array.isArray(value) && Object.keys(value).length > 0
}

async function loadProfile() {
  loading.value = true
  error.value = ''

  try {
    console.log('ProfileDisplayView: Loading profile for ID:', props.profileId)

    // Initialize the profile store if needed
    if (profileStore.userProfiles.length === 0) {
      console.log('ProfileDisplayView: Initializing profile store')
      await profileStore.initialize()
    }

    // Use the fetchProfile method from the profile store
    const profile = await profileStore.fetchProfile(props.profileId)

    if (!profile) {
      console.error('ProfileDisplayView: Profile not found in store')
      error.value = 'Profile not found'
      baseProfile.value = null
      profileData.value = null
      return
    }

    console.log('ProfileDisplayView: Profile loaded from store:', profile)

    // If first_name and last_name are not provided, generate them from email
    if ((!profile.first_name || profile.first_name.trim() === '') &&
        (!profile.last_name || profile.last_name.trim() === '') &&
        profile.email) {
      const generatedName = getNameFromEmail(profile.email);

      // If the generated name has a space, split it into first and last name
      if (generatedName.includes(' ')) {
        const nameParts = generatedName.split(' ');
        profile.first_name = profile.first_name || nameParts[0];
        profile.last_name = profile.last_name || nameParts.slice(1).join(' ');
      } else {
        // If no space, use the whole name as first name
        profile.first_name = profile.first_name || generatedName;
      }
    }

    baseProfile.value = profile

    // Explicitly load specialized profile data if not already loaded
    if (profile.profile_type && !profileStore.currentSpecializedProfile.value) {
      console.log('ProfileDisplayView: Explicitly loading specialized profile data for type:', profile.profile_type)
      try {
        // Load the specialized profile
        const specializedProfile = await profileService.loadSpecializedProfile(props.profileId, profile.profile_type)

        if (specializedProfile) {
          console.log('ProfileDisplayView: Specialized profile loaded:', specializedProfile)
          // Update the store with the specialized profile
          profileStore.setSpecializedProfile(specializedProfile)

          // Merge with base profile
          profileData.value = {
            ...profile,
            ...specializedProfile
          }
          source.value = `${profile.profile_type}_profiles`
        } else {
          console.warn('ProfileDisplayView: No specialized profile data found')
          profileData.value = profile
          source.value = 'personal_details'
        }
      } catch (err) {
        console.error('ProfileDisplayView: Error loading specialized profile data:', err)
        // Continue anyway, we'll just show the base profile
        profileData.value = profile
        source.value = 'personal_details'
      }
    } else if (profileStore.currentSpecializedProfile.value) {
      console.log('ProfileDisplayView: Using specialized profile data from store:', profileStore.currentSpecializedProfile.value)
      profileData.value = {
        ...profile,
        ...profileStore.currentSpecializedProfile.value
      }
      source.value = `${profile.profile_type}_profiles`
    } else {
      profileData.value = profile
      source.value = 'personal_details'
    }
  } catch (err) {
    console.error('ProfileDisplayView: Error loading profile:', err)
    error.value = err.message || 'Failed to load profile'
  } finally {
    loading.value = false
  }
}

// Lifecycle hooks
onMounted(async () => {
  await loadProfile()
})
</script>

<style scoped>
.profile-display-view {
  max-width: 1200px;
  margin: 0 auto;
}

/* Profile Header Styling */
.profile-header-card {
  overflow: hidden;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.profile-header-section {
  padding: 20px;
}

.profile-avatar {
  border: 4px solid white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.profile-info {
  text-align: left;
}

@media (max-width: 599px) {
  .profile-info {
    text-align: center;
  }
}

.profile-name {
  font-size: 1.8rem;
  font-weight: 500;
  line-height: 1.2;
}

.profile-type {
  font-size: 1.1rem;
  opacity: 0.9;
}

.profile-email {
  font-size: 0.9rem;
  opacity: 0.8;
}

.section-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-header {
  padding: 12px 16px;
}

/* Profile Completion Styling */
.profile-completion {
  background-color: white;
  padding: 16px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
