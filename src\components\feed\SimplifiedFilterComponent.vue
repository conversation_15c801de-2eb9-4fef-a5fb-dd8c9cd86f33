<template>
  <q-card class="filter-card">
    <q-card-section class="q-pa-md">
      <!-- AI Assistance Section -->
      <div class="ai-triggers-section q-mb-md">
        <div class="text-subtitle2 text-weight-medium q-mb-sm">
          <q-icon name="psychology" color="primary" size="sm" class="q-mr-xs" />
          AI Assistance
        </div>
        <div class="row q-gutter-xs">
          <template v-for="trigger in currentTabTriggers" :key="trigger.key">
            <AITriggerButton
              :trigger-key="trigger.key"
              :label="trigger.label"
              :icon="trigger.icon"
              :color="trigger.color || 'primary'"
              :tooltip="trigger.tooltip"
              :context="`community-${activeTab}`"
              size="sm"
              variant="compact"
              @triggered="onTriggerActivated"
              @success="onTriggerSuccess"
              @error="onTriggerError"
            />
          </template>
        </div>
      </div>

      <!-- Filters Section -->
      <div class="text-h6 text-primary q-mb-md">
        <q-icon name="filter_list" color="primary" size="sm" class="q-mr-xs" />
        Filters
      </div>

      <!-- AI Search Button -->
      <div class="q-mb-md">
        <AITriggerButton
          trigger-key="ai_search"
          label="AI Search"
          icon="search"
          color="primary"
          tooltip="Use AI to search and discover content with natural language"
          :context="`community-${activeTab}`"
          size="md"
          variant="compact"
          class="full-width"
          @triggered="onTriggerActivated"
          @success="onTriggerSuccess"
          @error="onTriggerError"
        />
      </div>

      <!-- Date Range Filter -->
      <div class="q-mb-md">
        <q-select
          v-model="dateRange"
          :options="dateRangeOptions"
          outlined
          dense
          emit-value
          map-options
          option-value="value"
          option-label="label"
          @update:model-value="handleDateRangeChange"
          class="compact-select"
        >
          <template v-slot:prepend>
            <q-icon name="event" color="primary" />
          </template>
          <template v-slot:append>
            <q-icon
              name="close"
              class="cursor-pointer"
              @click.stop="clearDateRange"
              v-if="dateRange !== 'all'"
            />
          </template>
        </q-select>
      </div>

      <!-- Dynamic Filters Based on Active Tab -->
      <template v-if="activeTab === 'feed'">
        <!-- Main Categories (always on feed tab) -->
        <div class="q-mb-md">
          <div class="text-subtitle2 q-mb-sm">Categories</div>
          <div class="category-chips">
            <q-chip
              v-for="category in mainCategories"
              :key="category.value"
              :selected="selectedMainCategory === category.value"
              @click="selectMainCategory(category.value)"
              :color="selectedMainCategory === category.value ? 'primary' : 'grey-3'"
              :text-color="selectedMainCategory === category.value ? 'white' : 'dark'"
              clickable
              :icon="category.icon"
              class="q-ma-xs"
            >
              {{ category.label }}
            </q-chip>
          </div>
        </div>

        <!-- Tags for Selected Category -->
        <div v-if="selectedMainCategory && availableTags.length > 0" class="q-mb-md">
          <div class="text-subtitle2 q-mb-sm">Tags</div>
          <div class="tag-chips">
            <q-chip
              v-for="tag in availableTags"
              :key="tag"
              :selected="selectedTags.includes(tag)"
              @click="toggleTag(tag)"
              :color="selectedTags.includes(tag) ? 'secondary' : 'grey-2'"
              :text-color="selectedTags.includes(tag) ? 'white' : 'dark'"
              clickable
              size="sm"
              class="q-ma-xs"
            >
              {{ tag }}
            </q-chip>
          </div>
        </div>
      </template>

      <template v-else-if="activeTab === 'profiles'">
        <!-- Profile Types -->
        <div class="q-mb-md">
          <div class="text-subtitle2 q-mb-sm">Profile Types</div>
          <div class="category-chips">
            <q-chip
              v-for="type in profileTypes"
              :key="type.value"
              :selected="selectedProfileTypes.includes(type.value)"
              @click="toggleProfileType(type.value)"
              :color="selectedProfileTypes.includes(type.value) ? 'primary' : 'grey-3'"
              :text-color="selectedProfileTypes.includes(type.value) ? 'white' : 'dark'"
              clickable
              :icon="type.icon"
              class="q-ma-xs"
            >
              {{ type.label }}
            </q-chip>
          </div>
        </div>
      </template>

      <template v-else-if="activeTab === 'blog'">
        <!-- Blog Categories -->
        <div class="q-mb-md">
          <div class="text-subtitle2 q-mb-sm">Blog Categories</div>
          <div class="category-chips">
            <q-chip
              v-for="category in blogCategories"
              :key="category.value"
              :selected="selectedBlogCategories.includes(category.value)"
              @click="toggleBlogCategory(category.value)"
              :color="selectedBlogCategories.includes(category.value) ? 'primary' : 'grey-3'"
              :text-color="selectedBlogCategories.includes(category.value) ? 'white' : 'dark'"
              clickable
              :icon="category.icon"
              class="q-ma-xs"
            >
              {{ category.label }}
            </q-chip>
          </div>
        </div>

        <!-- Read Time -->
        <div class="q-mb-md">
          <div class="text-subtitle2 q-mb-sm">Read Time</div>
          <div class="category-chips">
            <q-chip
              v-for="time in readTimeOptions"
              :key="time.value"
              :selected="selectedReadTime === time.value"
              @click="selectReadTime(time.value)"
              :color="selectedReadTime === time.value ? 'secondary' : 'grey-3'"
              :text-color="selectedReadTime === time.value ? 'white' : 'dark'"
              clickable
              :icon="time.icon"
              size="sm"
              class="q-ma-xs"
            >
              {{ time.label }}
            </q-chip>
          </div>
        </div>
      </template>

      <template v-else-if="activeTab === 'events'">
        <!-- Event Types -->
        <div class="q-mb-md">
          <div class="text-subtitle2 q-mb-sm">Event Types</div>
          <div class="category-chips">
            <q-chip
              v-for="type in eventTypes"
              :key="type.value"
              :selected="selectedEventTypes.includes(type.value)"
              @click="toggleEventType(type.value)"
              :color="selectedEventTypes.includes(type.value) ? 'primary' : 'grey-3'"
              :text-color="selectedEventTypes.includes(type.value) ? 'white' : 'dark'"
              clickable
              :icon="type.icon"
              class="q-ma-xs"
            >
              {{ type.label }}
            </q-chip>
          </div>
        </div>

        <!-- Event Format -->
        <div class="q-mb-md">
          <div class="text-subtitle2 q-mb-sm">Format</div>
          <div class="category-chips">
            <q-chip
              v-for="format in eventFormats"
              :key="format.value"
              :selected="selectedEventFormats.includes(format.value)"
              @click="toggleEventFormat(format.value)"
              :color="selectedEventFormats.includes(format.value) ? 'secondary' : 'grey-3'"
              :text-color="selectedEventFormats.includes(format.value) ? 'white' : 'dark'"
              clickable
              :icon="format.icon"
              size="sm"
              class="q-ma-xs"
            >
              {{ format.label }}
            </q-chip>
          </div>
        </div>
      </template>

      <template v-else-if="activeTab === 'groups'">
        <!-- Group Categories -->
        <div class="q-mb-md">
          <div class="text-subtitle2 q-mb-sm">Group Categories</div>
          <div class="category-chips">
            <q-chip
              v-for="category in groupCategories"
              :key="category.value"
              :selected="selectedGroupCategories.includes(category.value)"
              @click="toggleGroupCategory(category.value)"
              :color="selectedGroupCategories.includes(category.value) ? 'primary' : 'grey-3'"
              :text-color="selectedGroupCategories.includes(category.value) ? 'white' : 'dark'"
              clickable
              :icon="category.icon"
              class="q-ma-xs"
            >
              {{ category.label }}
            </q-chip>
          </div>
        </div>
      </template>

      <template v-else-if="activeTab === 'marketplace'">
        <!-- Listing Types -->
        <div class="q-mb-md">
          <div class="text-subtitle2 q-mb-sm">Listing Types</div>
          <div class="category-chips">
            <q-chip
              v-for="type in listingTypes"
              :key="type.value"
              :selected="selectedListingTypes.includes(type.value)"
              @click="toggleListingType(type.value)"
              :color="selectedListingTypes.includes(type.value) ? 'primary' : 'grey-3'"
              :text-color="selectedListingTypes.includes(type.value) ? 'white' : 'dark'"
              clickable
              :icon="type.icon"
              class="q-ma-xs"
            >
              {{ type.label }}
            </q-chip>
          </div>
        </div>
      </template>

      <!-- Special Filters -->
      <div class="q-mb-md">
        <div class="text-subtitle2 q-mb-sm">Special</div>
        <div class="special-chips">
          <q-chip
            :selected="showFeatured"
            @click="showFeatured = !showFeatured; applyFilters()"
            :color="showFeatured ? 'orange' : 'grey-2'"
            :text-color="showFeatured ? 'white' : 'dark'"
            clickable
            icon="star"
            size="sm"
            class="q-ma-xs"
          >
            Featured
          </q-chip>
        </div>
      </div>

      <!-- Reset Filters -->
      <div class="q-mt-md">
        <q-btn
          outline
          color="grey"
          class="full-width"
          label="Reset Filters"
          icon="refresh"
          @click="resetFilters"
          size="sm"
        />
      </div>
    </q-card-section>
  </q-card>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useFilterStore } from '@/stores/filterStore'
import AITriggerButton from '@/components/ai/AITriggerButton.vue'

// Props
interface Props {
  activeTab: string
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'filter-changed': [filters: any]
  'trigger-activated': [triggerKey: string]
  'trigger-success': [result: any]
  'trigger-error': [error: any]
}>()

// Store
const filterStore = useFilterStore()

// Reactive state
const dateRange = ref('all')
const selectedMainCategory = ref('')
const selectedTags = ref<string[]>([])
const selectedProfileTypes = ref<string[]>([])
const selectedBlogCategories = ref<string[]>([])
const selectedReadTime = ref('any')
const selectedEventTypes = ref<string[]>([])
const selectedEventFormats = ref<string[]>([])
const selectedGroupCategories = ref<string[]>([])
const selectedListingTypes = ref<string[]>([])
const showFeatured = ref(false)

// Date range options
const dateRangeOptions = ref([
  { label: 'All Time', value: 'all' },
  { label: 'Today', value: 'today' },
  { label: 'This Week', value: 'week' },
  { label: 'This Month', value: 'month' },
  { label: 'This Year', value: 'year' }
])

// Main categories (always on feed tab)
const mainCategories = ref([
  { label: 'All Posts', value: '', icon: 'feed' },
  { label: 'General', value: 'general', icon: 'chat' },
  { label: 'Profiles', value: 'profiles', icon: 'people' },
  { label: 'Marketplace', value: 'marketplace', icon: 'storefront' },
  { label: 'Groups', value: 'groups', icon: 'groups' },
  { label: 'Blog', value: 'blog', icon: 'article' },
  { label: 'Events', value: 'events', icon: 'event' }
])

// Tag mappings for each main category
const categoryTags = ref({
  general: ['innovation', 'startup', 'technology', 'networking', 'collaboration'],
  profiles: ['mentor', 'investor', 'entrepreneur', 'student', 'researcher'],
  marketplace: ['product', 'service', 'equipment', 'space', 'job', 'free'],
  groups: ['fintech', 'agritech', 'healthtech', 'edtech', 'cleantech'],
  blog: ['insights', 'trends', 'funding', 'success', 'research', 'news'],
  events: ['workshop', 'conference', 'networking', 'training', 'competition']
})

// Profile types
const profileTypes = ref([
  { label: 'Mentors', value: 'mentor', icon: 'school' },
  { label: 'Investors', value: 'investor', icon: 'account_balance' },
  { label: 'Entrepreneurs', value: 'entrepreneur', icon: 'business' },
  { label: 'Students', value: 'student', icon: 'person' },
  { label: 'Researchers', value: 'researcher', icon: 'science' }
])

// Blog categories
const blogCategories = ref([
  { label: 'Insights', value: 'insights', icon: 'lightbulb' },
  { label: 'Trends', value: 'trends', icon: 'trending_up' },
  { label: 'Funding', value: 'funding', icon: 'monetization_on' },
  { label: 'Success Stories', value: 'success', icon: 'star' },
  { label: 'Research', value: 'research', icon: 'science' },
  { label: 'News', value: 'news', icon: 'newspaper' }
])

// Read time options
const readTimeOptions = ref([
  { label: 'Any Length', value: 'any', icon: 'schedule' },
  { label: '< 5 min', value: 'short', icon: 'timer' },
  { label: '5-15 min', value: 'medium', icon: 'timer' },
  { label: '> 15 min', value: 'long', icon: 'timer' }
])

// Event types
const eventTypes = ref([
  { label: 'Workshops', value: 'workshop', icon: 'build' },
  { label: 'Conferences', value: 'conference', icon: 'event' },
  { label: 'Networking', value: 'networking', icon: 'people' },
  { label: 'Training', value: 'training', icon: 'school' },
  { label: 'Competitions', value: 'competition', icon: 'emoji_events' }
])

// Event formats
const eventFormats = ref([
  { label: 'Online', value: 'online', icon: 'computer' },
  { label: 'In-Person', value: 'offline', icon: 'location_on' },
  { label: 'Hybrid', value: 'hybrid', icon: 'hub' }
])

// Group categories
const groupCategories = ref([
  { label: 'FinTech', value: 'fintech', icon: 'account_balance' },
  { label: 'AgriTech', value: 'agritech', icon: 'agriculture' },
  { label: 'HealthTech', value: 'healthtech', icon: 'health_and_safety' },
  { label: 'EdTech', value: 'edtech', icon: 'school' },
  { label: 'CleanTech', value: 'cleantech', icon: 'eco' }
])

// Listing types
const listingTypes = ref([
  { label: 'Products', value: 'product', icon: 'inventory' },
  { label: 'Services', value: 'service', icon: 'handyman' },
  { label: 'Equipment', value: 'equipment', icon: 'precision_manufacturing' },
  { label: 'Spaces', value: 'space', icon: 'business' },
  { label: 'Jobs', value: 'job', icon: 'work' },
  { label: 'Free Items', value: 'free', icon: 'volunteer_activism' }
])

// Feed tags based on selected category
const feedTagsMap = ref({
  general: ['innovation', 'startup', 'technology', 'networking', 'collaboration'],
  news: ['platform', 'milestone', 'community', 'growth', 'government', 'funding', 'startups', 'grants'],
  startup: ['startup', 'funding', 'technology', 'success', 'entrepreneur'],
  growth: ['fintech', 'mobile', 'payments', 'inclusion', 'scaling'],
  research: ['agriculture', 'research', 'innovation', 'sustainability', 'academic']
})

// Computed feed tags
const feedTags = computed(() => {
  if (!selectedFeedCategory.value || !feedTagsMap.value[selectedFeedCategory.value]) {
    return []
  }
  return feedTagsMap.value[selectedFeedCategory.value]
})

// AI triggers for each tab
const aiTriggers = {
  feed: [
    {
      key: 'content_discovery',
      label: 'Discover Content',
      icon: 'explore',
      color: 'secondary',
      tooltip: 'Get AI help finding relevant content and discussions'
    },
    {
      key: 'networking',
      label: 'Find Connections',
      icon: 'people',
      color: 'accent',
      tooltip: 'Get AI help with networking and connecting with people'
    }
  ],
  profiles: [
    {
      key: 'find_mentors',
      label: 'Find Mentors',
      icon: 'school',
      color: 'primary',
      tooltip: 'Get AI help finding mentors in your field'
    }
  ],
  marketplace: [
    {
      key: 'find_products',
      label: 'Find Products',
      icon: 'shopping_cart',
      color: 'primary',
      tooltip: 'Get AI help finding relevant products and services'
    }
  ],
  groups: [
    {
      key: 'find_groups',
      label: 'Find Groups',
      icon: 'groups',
      color: 'primary',
      tooltip: 'Get AI help finding relevant groups to join'
    }
  ],
  blog: [
    {
      key: 'find_articles',
      label: 'Find Articles',
      icon: 'article',
      color: 'primary',
      tooltip: 'Get AI help finding relevant articles and insights'
    }
  ],
  events: [
    {
      key: 'find_events',
      label: 'Find Events',
      icon: 'event',
      color: 'primary',
      tooltip: 'Get AI help finding relevant events and workshops'
    }
  ]
}

// Computed properties
const currentTabTriggers = computed(() => {
  return aiTriggers[props.activeTab as keyof typeof aiTriggers] || []
})

const availableTags = computed(() => {
  if (!selectedMainCategory.value || !categoryTags.value[selectedMainCategory.value as keyof typeof categoryTags.value]) {
    return []
  }
  return categoryTags.value[selectedMainCategory.value as keyof typeof categoryTags.value]
})

// Methods
function selectMainCategory(category: string) {
  selectedMainCategory.value = category
  selectedTags.value = [] // Reset tags when category changes
  applyFilters()
}

function toggleTag(tag: string) {
  const index = selectedTags.value.indexOf(tag)
  if (index > -1) {
    selectedTags.value.splice(index, 1)
  } else {
    selectedTags.value.push(tag)
  }
  applyFilters()
}

function handleDateRangeChange() {
  applyFilters()
}

function clearDateRange() {
  dateRange.value = 'all'
  applyFilters()
}

function resetFilters() {
  dateRange.value = 'all'
  selectedMainCategory.value = ''
  selectedTags.value = []
  selectedProfileTypes.value = []
  selectedBlogCategories.value = []
  selectedReadTime.value = 'any'
  selectedEventTypes.value = []
  selectedEventFormats.value = []
  selectedGroupCategories.value = []
  selectedListingTypes.value = []
  showFeatured.value = false
  applyFilters()
}

// Profile tab methods
function toggleProfileType(type: string) {
  const index = selectedProfileTypes.value.indexOf(type)
  if (index > -1) {
    selectedProfileTypes.value.splice(index, 1)
  } else {
    selectedProfileTypes.value.push(type)
  }
  applyFilters()
}

// Blog tab methods
function toggleBlogCategory(category: string) {
  const index = selectedBlogCategories.value.indexOf(category)
  if (index > -1) {
    selectedBlogCategories.value.splice(index, 1)
  } else {
    selectedBlogCategories.value.push(category)
  }
  applyFilters()
}

function selectReadTime(time: string) {
  selectedReadTime.value = time
  applyFilters()
}

// Event tab methods
function toggleEventType(type: string) {
  const index = selectedEventTypes.value.indexOf(type)
  if (index > -1) {
    selectedEventTypes.value.splice(index, 1)
  } else {
    selectedEventTypes.value.push(type)
  }
  applyFilters()
}

function toggleEventFormat(format: string) {
  const index = selectedEventFormats.value.indexOf(format)
  if (index > -1) {
    selectedEventFormats.value.splice(index, 1)
  } else {
    selectedEventFormats.value.push(format)
  }
  applyFilters()
}

// Group tab methods
function toggleGroupCategory(category: string) {
  const index = selectedGroupCategories.value.indexOf(category)
  if (index > -1) {
    selectedGroupCategories.value.splice(index, 1)
  } else {
    selectedGroupCategories.value.push(category)
  }
  applyFilters()
}

// Marketplace tab methods
function toggleListingType(type: string) {
  const index = selectedListingTypes.value.indexOf(type)
  if (index > -1) {
    selectedListingTypes.value.splice(index, 1)
  } else {
    selectedListingTypes.value.push(type)
  }
  applyFilters()
}

function applyFilters() {
  let filters: any = {
    dateRange: dateRange.value,
    featured: showFeatured.value,
    activeTab: props.activeTab
  }

  // Add tab-specific filters
  switch (props.activeTab) {
    case 'feed':
      filters = {
        ...filters,
        mainCategory: selectedMainCategory.value,
        tags: selectedTags.value
      }
      break
    case 'profiles':
      filters = {
        ...filters,
        profileTypes: selectedProfileTypes.value
      }
      break
    case 'blog':
      filters = {
        ...filters,
        blogCategories: selectedBlogCategories.value,
        readTime: selectedReadTime.value
      }
      break
    case 'events':
      filters = {
        ...filters,
        eventTypes: selectedEventTypes.value,
        eventFormats: selectedEventFormats.value
      }
      break
    case 'groups':
      filters = {
        ...filters,
        groupCategories: selectedGroupCategories.value
      }
      break
    case 'marketplace':
      filters = {
        ...filters,
        listingTypes: selectedListingTypes.value
      }
      break
  }

  emit('filter-changed', filters)
}

// Event handlers
function onTriggerActivated(triggerKey: string) {
  emit('trigger-activated', triggerKey)
}

function onTriggerSuccess(result: any) {
  emit('trigger-success', result)
}

function onTriggerError(error: any) {
  emit('trigger-error', error)
}

// Watch for tab changes
watch(() => props.activeTab, () => {
  resetFilters()
})
</script>

<style scoped>
.filter-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.ai-triggers-section {
  background-color: rgba(13, 138, 62, 0.05);
  border-radius: 6px;
  padding: 8px;
  border: 1px solid rgba(13, 138, 62, 0.1);
}

.compact-select {
  min-height: 36px;
}

.category-chips,
.tag-chips,
.special-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin: 0;
}

.category-chips .q-chip {
  font-size: 0.8rem;
  min-height: 28px;
}

.tag-chips .q-chip {
  font-size: 0.75rem;
  min-height: 24px;
}

.special-chips .q-chip {
  font-size: 0.75rem;
  min-height: 24px;
}
</style>
