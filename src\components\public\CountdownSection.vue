<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();

// Navigation functions
function navigateTo(path) {
  router.push(path);
}
</script>

<template>
  <section class="countdown-section q-py-sm" role="region" aria-label="Innovation Hub Features">
    <div class="container q-mx-auto q-px-md">
      <div class="row">
        <div class="col-1" />
        <div class="col-10">
          <div class="text-center">
            <h2 class="text-h3 text-weight-light q-mb-md">Innovation Ecosystem</h2>

            <p class="text-body1 q-mb-xl text-center">
              ZbInnovation is bringing you a vibrant ecosystem that fosters innovation. By combining the power of a state-of-the-art physical hub with a cutting-edge virtual community, we create a unique environment for collaboration between all players in the innovation ecosystem.
            </p>

            <div class="features-grid">
              <div class="feature-card clickable" @click="navigateTo('/virtual-community?tab=feed')">
                <span class="icon icon-connect"></span>
                <h3>Connect</h3>
                <p>Connect with other innovators, mentors, and resources in the ecosystem.</p>
                <q-btn flat color="primary" class="q-mt-sm" label="Community Feed" icon-right="arrow_forward" />
              </div>
              <div class="feature-card clickable" @click="navigateTo('/virtual-community?tab=profiles')">
                <span class="icon icon-network"></span>
                <h3>Network</h3>
                <p>Build valuable relationships with industry leaders and potential partners.</p>
                <q-btn flat color="primary" class="q-mt-sm" label="Profiles Directory" icon-right="arrow_forward" />
              </div>
              <div class="feature-card clickable" @click="navigateTo('/virtual-community?tab=marketplace')">
                <span class="icon icon-grow"></span>
                <h3>Grow</h3>
                <p>Access resources and support to help your innovation thrive.</p>
                <q-btn flat color="primary" class="q-mt-sm" label="Marketplace" icon-right="arrow_forward" />
              </div>
            </div>
          </div>
        </div>
        <div class="col-1" />
      </div>
    </div>
  </section>
</template>

<style scoped>
.countdown-section {
  padding: 20px 0;
  margin-top: -20px;
  background: white;
}

.container {
  max-width: 1400px;
}

.timer-card {
  border-radius: 15px;
  background: #f5f5f5;
  margin-bottom: 1rem;
  padding: 1rem;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
  margin-top: 48px;
}

@media (max-width: 767px) {
  .features-grid {
    grid-template-columns: 1fr;
  }
}

.feature-card {
  background: #f5f5f5;
  border-radius: 12px;
  padding: 24px;
  text-align: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.feature-card.clickable {
  cursor: pointer;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  background: #e8f5e9;
}

.feature-card .icon {
  width: 48px;
  height: 48px;
  margin: 0 auto 16px;
  display: inline-block;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  color: #0D8A3E;
}

.icon-connect {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%230D8A3E' d='M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6.59-6.59 4 4 6.3-6.29L22 11V5h-6z'/%3E%3C/svg%3E");
}

.icon-network {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%230D8A3E' d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zM8 17.5a2.5 2.5 0 0 1 0-5 2.5 2.5 0 0 1 0 5zM12 11a2.5 2.5 0 0 1 0-5 2.5 2.5 0 0 1 0 5zm4 6.5a2.5 2.5 0 0 1 0-5 2.5 2.5 0 0 1 0 5z'/%3E%3C/svg%3E");
}

.icon-grow {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%230D8A3E' d='M12 8l-6 6 1.41 1.41L12 10.83l4.59 4.58L18 14l-6-6zM12 2l-6 6 1.41 1.41L12 4.83l4.59 4.58L18 8l-6-6z'/%3E%3C/svg%3E");
}

.feature-card h3 {
  color: #0D8A3E;
  font-size: 1.5rem;
  margin: 0 0 12px;
  font-weight: 500;
}

.feature-card p {
  color: #666666;
  margin: 0;
  font-size: 1rem;
  line-height: 1.5;
}

@media (max-width: 599px) {
  .countdown-section {
    padding: 15px 0;
  }

  .container > .row > .col-1 {
    display: none;
  }

  .container > .row > .col-10 {
    width: 100%;
  }

  .timer-card {
    padding: 0.5rem;
  }
}
</style>
