/**
 * Streamlined Feed Data Service
 *
 * Optimized data service for virtual community with minimal overhead
 */

import { supabase } from '../lib/supabase';
import { usePostsStore } from '../stores/posts';
import { useProfileStore } from '../stores/profile';
import { useEventsStore } from '../stores/events';
import { useConnectionService } from './connectionService';
import { mapPostFromDatabase } from '../types/post';

export interface PaginationState {
  page: number;
  limit: number;
  cursor: string | null;
  scrollPosition: number;
}

export interface FeedFilters {
  searchQuery?: string;
  postTypes?: string[];
  subTypes?: string[];
  dateRange?: string;
  tags?: string[];
  profileTypes?: string[];
  listingTypes?: string[];
  priceRange?: { min: number; max: number };
}

export class FeedDataService {
  private postsStore = usePostsStore();
  private profileStore = useProfileStore();
  private eventsStore = useEventsStore();
  private connectionService = useConnectionService();

  // Simple loading state tracking
  private loadingStates: { [key: string]: boolean } = {};

  /**
   * Prevent duplicate requests
   */
  private async withLoadingState<T>(key: string, operation: () => Promise<T>): Promise<T> {
    if (this.loadingStates[key]) {
      // Wait for existing request
      while (this.loadingStates[key]) {
        await new Promise(resolve => setTimeout(resolve, 50));
      }
    }

    this.loadingStates[key] = true;
    try {
      return await operation();
    } finally {
      this.loadingStates[key] = false;
    }
  }

  /**
   * Streamlined feed posts fetching - MAX 10 posts per page
   */
  async fetchFeedPosts(pagination: PaginationState, filters: FeedFilters = {}) {
    const key = `feed-${pagination.page}`;

    return this.withLoadingState(key, async () => {
      // Enforce maximum 10 posts per page
      const MAX_POSTS_PER_PAGE = 10;
      const enforcedLimit = pagination.limit > MAX_POSTS_PER_PAGE ? MAX_POSTS_PER_PAGE : pagination.limit;

      const postFilters = {
        searchQuery: filters.searchQuery || '',
        postTypes: filters.postTypes || [],
        subTypes: filters.subTypes || [],
        dateRange: filters.dateRange || 'all',
        tags: filters.tags || [],
        page: pagination.page,
        limit: enforcedLimit,
        paginationMode: pagination.page > 1 ? 'append' as const : 'replace' as const
      };

      console.log('feedDataService: Fetching posts with enforced limit:', enforcedLimit);
      await this.postsStore.fetchPosts(postFilters);

      return {
        posts: [...this.postsStore.filteredPosts],
        hasMore: this.postsStore.hasMorePosts,
        nextCursor: this.postsStore.nextCursor
      };
    });
  }

  /**
   * Streamlined profiles fetching
   */
  async fetchProfiles(pagination: PaginationState, filters: FeedFilters = {}) {
    const key = `profiles-${pagination.page}`;

    return this.withLoadingState(key, async () => {
      const profileFilters = {
        searchQuery: filters.searchQuery || '',
        profileType: filters.profileTypes || []
      };

      await this.profileStore.loadPublicProfiles(pagination.page, pagination.limit, profileFilters);

      // Get current user once
      const { data: userData } = await supabase.auth.getUser();
      const currentUser = userData.user;

      // Batch process connection statuses for better performance
      const profiles = this.profileStore.publicProfiles;
      const userIds = profiles
        .filter((profile: any) => currentUser && profile.user_id !== currentUser.id)
        .map((profile: any) => profile.user_id);

      // Batch fetch connection statuses if we have user IDs
      const connectionStatuses: { [key: string]: string } = {};
      if (userIds.length > 0 && currentUser) {
        try {
          // Optimize: Get all connection statuses in a single query
          const { data: connections } = await supabase
            .from('connections')
            .select('user_id, connected_user_id, status')
            .or(`user_id.eq.${currentUser.id},connected_user_id.eq.${currentUser.id}`)
            .in('user_id', [...userIds, currentUser.id])
            .in('connected_user_id', [...userIds, currentUser.id]);

          if (connections) {
            connections.forEach(conn => {
              const otherUserId = conn.user_id === currentUser.id ? conn.connected_user_id : conn.user_id;
              if (userIds.includes(otherUserId)) {
                connectionStatuses[otherUserId] = conn.status;
              }
            });
          }
        } catch (err) {
          // Silent fail for connection status
        }
      }

      // Map profiles with optimized connection status lookup
      const mappedProfiles = profiles.map((profile: any) => {
        const connectionStatus = connectionStatuses[profile.user_id] || 'none';

        return {
          id: profile.user_id,
          user_id: profile.user_id,
          name: profile.first_name && profile.last_name
            ? `${profile.first_name} ${profile.last_name}`
            : profile.profile_name || profile.email?.split('@')[0] || 'Anonymous',
          first_name: profile.first_name,
          last_name: profile.last_name,
          profile_name: profile.profile_name,
          profile_type: profile.profile_type,
          profileType: profile.profile_type,
          avatar: profile.avatar_url,
          avatar_url: profile.avatar_url,
          bio: profile.bio || profile.base_bio || profile.specialized_bio || '',
          base_bio: profile.base_bio,
          specialized_bio: profile.specialized_bio,
          email: profile.email,
          contact_email: profile.contact_email,
          website: profile.website,
          linkedin: profile.linkedin,
          profile_completion: profile.profile_completion || 0,
          completion: profile.profile_completion || 0,
          location: profile.location || profile.city || '',
          joinDate: profile.created_at || new Date().toISOString(),
          created_at: profile.created_at,
          updated_at: profile.updated_at,
          isConnected: connectionStatus === 'accepted',
          isPending: connectionStatus === 'pending',
          isIncomingPending: connectionStatus === 'incoming_pending',
          isCurrentUser: currentUser ? profile.user_id === currentUser.id : false
        };
      });

      // Sort profiles to show current user first (only on first page)
      let sortedProfiles = mappedProfiles;
      if (pagination.page === 1 && currentUser) {
        sortedProfiles = mappedProfiles.sort((a, b) => {
          // Current user first
          if (a.isCurrentUser && !b.isCurrentUser) return -1;
          if (!a.isCurrentUser && b.isCurrentUser) return 1;
          // Then by profile completion (higher first)
          return (b.profile_completion || 0) - (a.profile_completion || 0);
        });
      }

      return {
        profiles: sortedProfiles,
        hasMore: this.profileStore.hasMorePublicProfiles
      };
    });
  }

  /**
   * Streamlined articles fetching - MAX 10 articles per page
   */
  async fetchArticles(pagination: PaginationState, filters: FeedFilters = {}) {
    const key = `articles-${pagination.page}`;

    return this.withLoadingState(key, async () => {
      // Enforce maximum 10 articles per page
      const MAX_POSTS_PER_PAGE = 10;
      const enforcedLimit = pagination.limit > MAX_POSTS_PER_PAGE ? MAX_POSTS_PER_PAGE : pagination.limit;

      const articleFilters = {
        postTypes: ['BLOG'],
        subTypes: ['blog', 'blog_article'],
        searchQuery: filters.searchQuery,
        tags: filters.tags,
        page: pagination.page,
        limit: enforcedLimit,
        cursor: pagination.cursor
      };

      await this.postsStore.fetchPosts(articleFilters);

      const blogPosts = this.postsStore.filteredPosts.filter((post: any) =>
        post.postType === 'BLOG' || post.subType === 'blog' || post.subType === 'BLOG' ||
        post.postType === 'SUCCESS_STORY' || post.subType === 'SUCCESS_STORY'
      );

      const articles = blogPosts.map((post: any) => ({
        id: post.id,
        title: post.title || post.blogTitle || '',
        excerpt: post.excerpt || '',
        content: post.content || post.blogFullContent || '',
        image: post.featuredImage || '',
        category: post.blogCategory || '',
        date: post.createdAt || '',
        author: post.author || 'Anonymous',
        readTime: '5',
        slug: post.slug || `article-${post.id}`,
        postType: post.postType
      }));

      return {
        articles,
        hasMore: blogPosts.length >= pagination.limit,
        nextCursor: this.postsStore.nextCursor
      };
    });
  }

  /**
   * Fetch events using the events store - MAX 10 events per page
   */
  async fetchEvents(pagination: PaginationState, filters: FeedFilters = {}) {
    await this.eventsStore.fetchEvents();

    // Enforce maximum 10 events per page
    const MAX_EVENTS_PER_PAGE = 10;
    const enforcedLimit = pagination.limit > MAX_EVENTS_PER_PAGE ? MAX_EVENTS_PER_PAGE : pagination.limit;

    // Get events from the store and apply pagination
    const allEvents = this.eventsStore.events;
    const startIndex = (pagination.page - 1) * enforcedLimit;
    const endIndex = startIndex + enforcedLimit;
    const paginatedEvents = allEvents.slice(startIndex, endIndex);

    console.log('fetchEvents: Returning', paginatedEvents.length, 'events with enforced limit:', enforcedLimit);

    return {
      events: paginatedEvents,
      hasMore: endIndex < allEvents.length
    };
  }

  /**
   * Fetch groups using the posts store - MAX 10 groups per page
   */
  async fetchGroups(pagination: PaginationState, filters: FeedFilters = {}) {
    // Enforce maximum 10 groups per page
    const MAX_GROUPS_PER_PAGE = 10;
    const enforcedLimit = pagination.limit > MAX_GROUPS_PER_PAGE ? MAX_GROUPS_PER_PAGE : pagination.limit;

    const groupFilters = {
      postTypes: ['GROUP'],
      searchQuery: filters.searchQuery,
      tags: filters.tags,
      page: pagination.page,
      limit: enforcedLimit,
      cursor: pagination.cursor
    };

    await this.postsStore.fetchPosts(groupFilters);

    const groupPosts = this.postsStore.filteredPosts.filter((post: any) => post.postType === 'GROUP');

    const groups = groupPosts.map((post: any) => ({
      id: post.id,
      name: post.title || '',
      description: post.content || '',
      image: post.featuredImage || '',
      category: post.subType || post.groupCategory || 'GENERAL',
      memberCount: post.memberCount || Math.floor(Math.random() * 50) + 5,
      createdAt: post.createdAt || '',
      isJoined: post.isJoined || false
    }));

    return {
      groups,
      hasMore: groupPosts.length >= enforcedLimit,
      nextCursor: this.postsStore.nextCursor
    };
  }

  /**
   * Fetch marketplace listings using direct database query - MAX 10 listings per page
   */
  async fetchMarketplace(pagination: PaginationState, filters: FeedFilters = {}) {
    // Enforce maximum 10 marketplace listings per page
    const MAX_LISTINGS_PER_PAGE = 10;
    const enforcedLimit = pagination.limit > MAX_LISTINGS_PER_PAGE ? MAX_LISTINGS_PER_PAGE : pagination.limit;

    let query = supabase
      .from('posts_with_authors')
      .select('*')
      .eq('post_type', 'platform')
      .eq('sub_type', 'marketplace');

    // Apply filters
    if (filters.searchQuery?.trim()) {
      query = query.or(`title.ilike.%${filters.searchQuery}%,content.ilike.%${filters.searchQuery}%`);
    }

    if (filters.dateRange && filters.dateRange !== 'all') {
      const now = new Date();
      let dateThreshold: Date | undefined;

      switch (filters.dateRange) {
        case 'today':
          dateThreshold = new Date(now.getFullYear(), now.getMonth(), now.getDate());
          break;
        case 'week':
          dateThreshold = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case 'month':
          dateThreshold = new Date(now.getFullYear(), now.getMonth(), 1);
          break;
        case 'year':
          dateThreshold = new Date(now.getFullYear(), 0, 1);
          break;
      }

      if (dateThreshold) {
        query = query.gte('created_at', dateThreshold.toISOString());
      }
    }

    console.log('fetchMarketplace: Using enforced limit:', enforcedLimit);
    const { data, error } = await query
      .order('created_at', { ascending: false })
      .range(
        (pagination.page - 1) * enforcedLimit,
        pagination.page * enforcedLimit - 1
      );

    if (error) throw error;

    let marketplacePosts = data.map((post: any) => mapPostFromDatabase(post));

    // Apply client-side filtering for listing types
    if (filters.listingTypes?.length) {
      marketplacePosts = marketplacePosts.filter((post: any) => {
        let postListingType = post.listingType || post.category || 'product';
        return filters.listingTypes!.includes(postListingType);
      });
    }

    const marketplace = marketplacePosts.map((post: any) => ({
      id: post.id,
      title: post.title || '',
      description: post.content || '',
      image: post.featuredImage || '',
      price: post.price || '0',
      currency: post.currency || 'USD',
      type: post.subType || post.listingType || 'PRODUCT',
      seller: post.author || 'Anonymous',
      location: post.location || 'Harare',
      createdAt: post.createdAt || '',
      commentsCount: post.commentsCount || 0,
      likesCount: post.likesCount || 0,
      isLiked: post.isLiked || false,
      userId: post.userId,
      author: post.author
    }));

    return {
      marketplace,
      hasMore: marketplacePosts.length >= enforcedLimit
    };
  }
}

// Export singleton instance
export const feedDataService = new FeedDataService();
