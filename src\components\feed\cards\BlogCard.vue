<template>
  <q-card class="blog-card">
    <!-- Blog Categories at the top -->
    <div class="q-pa-sm bg-grey-2 row items-center">
      <q-badge
        color="purple"
        class="q-mr-sm"
        size="lg"
      >
        Blog
      </q-badge>
      <q-badge
        v-if="article.category"
        :color="getCategoryColor(article.category)"
        class="q-mr-sm"
        size="lg"
      >
        {{ article.category }}
      </q-badge>
    </div>

    <div class="blog-image-container">
      <q-img
        :src="articleImage"
        :ratio="16/9"
        class="blog-image"
        @error="handleImageError"
        no-spinner
        no-transition
      >
        <template v-slot:error>
          <div class="absolute-full flex flex-center bg-grey-3 text-grey-8">
            <div class="text-center">
              <q-icon name="broken_image" size="3em" />
              <div>Image failed to load</div>
              <q-btn
                v-if="isImageUrlFixable"
                flat
                color="primary"
                label="Try Fix URL"
                class="q-mt-sm"
                @click="tryFixImageUrl"
              />
            </div>
          </div>
        </template>
      </q-img>
    </div>

    <q-card-section>
      <div class="text-h6">{{ article.title }}</div>
      <div class="text-caption q-mb-sm">
        {{ article.date }} | {{ article.author }}
        <span v-if="article.readTime" class="q-ml-sm">
          <q-icon name="schedule" size="xs" /> {{ article.readTime }}
        </span>
      </div>
      <p class="text-body2">{{ truncatedExcerpt }}</p>

      <!-- Tags (if available) -->
      <div v-if="articleTags.length > 0" class="q-mt-sm">
        <q-chip
          v-for="tag in articleTags"
          :key="tag"
          size="sm"
          outline
          color="primary"
          class="q-mr-xs"
        >
          #{{ tag }}
        </q-chip>
      </div>
    </q-card-section>
    <q-separator />
    <q-card-actions align="right" class="blog-card-actions">
      <q-btn flat color="grey" icon="bookmark_border" @click="handleSave" />
      <q-btn flat color="grey" icon="share" @click="handleShare" />
      <q-btn flat color="primary" label="Read More" @click="handleReadMore" />
    </q-card-actions>
  </q-card>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { useRouter } from 'vue-router';
import { truncateText, stripHtml } from '../../../utils/textUtils';

// Local state
const fixedImageUrl = ref('');

const props = defineProps({
  article: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['read', 'share', 'save']);
const router = useRouter();

// Truncate article excerpt for feed view
const truncatedExcerpt = computed(() => {
  // Use excerpt if available, otherwise use content
  let textToTruncate = props.article.excerpt || props.article.content || '';

  // Extract content from JSON if needed
  if (typeof textToTruncate === 'string' && (textToTruncate.startsWith('{') || textToTruncate.startsWith('['))) {
    try {
      const parsedContent = JSON.parse(textToTruncate);

      // Handle different JSON structures
      if (parsedContent.description) {
        textToTruncate = parsedContent.description;
      } else if (parsedContent.content) {
        textToTruncate = parsedContent.content;
      } else if (parsedContent.blogContent) {
        textToTruncate = parsedContent.blogContent;
      } else if (typeof parsedContent === 'object') {
        // If it's an object but doesn't have expected fields, stringify it
        textToTruncate = JSON.stringify(parsedContent);
      }
    } catch (e) {
      // If parsing fails, it's not valid JSON, keep the original content
      console.log('Failed to parse content as JSON:', e);
    }
  }

  // Strip HTML tags if present
  if (/<[a-z][\s\S]*>/i.test(textToTruncate)) {
    textToTruncate = stripHtml(textToTruncate);
  }

  return truncateText(textToTruncate, 250);
});

// Get the article image from either featuredImage or image property
const articleImage = computed(() => {
  // If we have a fixed URL from a previous fix attempt, use that
  if (fixedImageUrl.value) {
    return fixedImageUrl.value;
  }

  const imageUrl = props.article.featuredImage || props.article.image || '';

  // Only log in development mode
  if (import.meta.env.MODE === 'development') {
    console.log('Blog image URL for article ID:', props.article.id, 'URL:', imageUrl);
  }

  return imageUrl;
});

// Check if the image URL is potentially fixable
const isImageUrlFixable = computed(() => {
  const url = props.article.featuredImage || props.article.image || '';

  // If it's empty, it's not fixable
  if (!url) return false;

  // If it already has the correct full Supabase URL format, it's not fixable
  if (url.includes('dpicnvisvxpmgjtbeicf.supabase.co/storage/v1/object/public/imagefiles/') &&
      !url.includes('imagefiles/imagefiles/')) {
    return false;
  }

  // If it's an external URL (not Supabase), it's not fixable
  if (url.startsWith('http') && !url.includes('supabase') && !url.includes('imagefiles')) {
    return false;
  }

  // If it has the wrong format (includes imagefiles but not in the right place), it's fixable
  if (url.includes('imagefiles/') &&
      (!url.includes('storage/v1/object/public/imagefiles/') ||
       url.includes('imagefiles/imagefiles/'))) {
    return true;
  }

  // Otherwise, we might be able to fix it
  return true;
});

// Try to fix the image URL by adding the Supabase storage prefix
function tryFixImageUrl() {
  const originalUrl = props.article.featuredImage || props.article.image || '';

  // If the URL is empty, there's nothing to fix
  if (!originalUrl) return;

  // Only log in development mode
  if (process.env.NODE_ENV === 'development') {
    console.log('Attempting to fix image URL:', originalUrl);
  }

  // If it's a relative path or partial Supabase path
  if (!originalUrl.startsWith('http')) {
    // If it already includes the bucket name
    if (originalUrl.includes('imagefiles/')) {
      // Extract the file path after 'imagefiles/'
      const match = originalUrl.match(/imagefiles\/(.+)$/);
      if (match && match[1]) {
        fixedImageUrl.value = `https://dpicnvisvxpmgjtbeicf.supabase.co/storage/v1/object/public/imagefiles/${match[1]}`;
      } else {
        fixedImageUrl.value = `https://dpicnvisvxpmgjtbeicf.supabase.co/storage/v1/object/public/${originalUrl}`;
      }
    } else {
      // Assume it's in the imagefiles bucket
      fixedImageUrl.value = `https://dpicnvisvxpmgjtbeicf.supabase.co/storage/v1/object/public/imagefiles/${originalUrl}`;
    }
  } else if (originalUrl.includes('supabase')) {
    if (!originalUrl.includes('/storage/v1/object/public/')) {
      // It's a Supabase URL but missing the storage path
      const parts = originalUrl.split('/');
      const fileName = parts[parts.length - 1];
      fixedImageUrl.value = `https://dpicnvisvxpmgjtbeicf.supabase.co/storage/v1/object/public/imagefiles/${fileName}`;
    } else if (originalUrl.includes('imagefiles/') && !originalUrl.includes('/storage/v1/object/public/imagefiles/')) {
      // It has the wrong format for the bucket path
      const match = originalUrl.match(/imagefiles\/(.+)$/);
      if (match && match[1]) {
        fixedImageUrl.value = `https://dpicnvisvxpmgjtbeicf.supabase.co/storage/v1/object/public/imagefiles/${match[1]}`;
      }
    }
  }

  // Only log in development mode
  if (process.env.NODE_ENV === 'development') {
    console.log('Fixed image URL:', fixedImageUrl.value);
  }
}

// Process article tags to ensure they're displayed correctly
const articleTags = computed(() => {
  if (!props.article.tags) return [];

  // If tags is a string (possibly from HTML), convert it to an array
  if (typeof props.article.tags === 'string') {
    // Try to parse as JSON if it looks like a JSON array
    if (props.article.tags.startsWith('[') && props.article.tags.endsWith(']')) {
      try {
        return JSON.parse(props.article.tags);
      } catch (e) {
        console.log('Failed to parse tags JSON:', e);
        // If parsing fails, split by comma as fallback
        return props.article.tags.replace(/[\[\]"']/g, '').split(',').map((tag: string) => tag.trim());
      }
    }
    // If it's not JSON-like, split by comma
    return props.article.tags.split(',').map((tag: string) => tag.trim());
  }

  // If it's already an array, process each item to handle object tags
  return props.article.tags.map((tag: any) => {
    // Check if the tag is an object with label/value properties (from select components)
    if (typeof tag === 'object' && tag !== null) {
      // Always prioritize the value property for display
      if (tag.value) return tag.value;
      // If no value, but has a label property, use that
      if (tag.label) return tag.label;
      // Otherwise return a generic tag name instead of stringifying
      return 'Tag';
    }
    // If it's a string or number, return as is
    return tag;
  });
});

// Methods
function handleReadMore() {
  emit('read', props.article.id);

  // If the article has a slug, navigate to it
  if (props.article.slug) {
    // Determine if we're in the virtual community context
    const isVirtualCommunity = window.location.pathname.includes('virtual-community');
    const routeName = isVirtualCommunity ? 'virtual-community-article' : 'article';
    router.push({ name: routeName, params: { slug: props.article.slug } });
  }
}

function handleShare() {
  emit('share', props.article.id);
}

function handleSave() {
  emit('save', props.article.id);
}

// Handle image loading errors
function handleImageError(err: Error) {
  // Only log in development mode
  if (process.env.NODE_ENV === 'development') {
    console.error('Image failed to load for article ID:', props.article.id, 'Error:', err);
    console.error('Failed image URL:', articleImage.value);
  }

  // If the URL is from Supabase but doesn't have the full path, suggest a fix
  if (isImageUrlFixable.value && process.env.NODE_ENV === 'development') {
    console.log('This image URL might be fixable. Try clicking the "Try Fix URL" button.');
  }
}

// Helper function to get category color
function getCategoryColor(category: string): string {
  const categoryColors: Record<string, string> = {
    'Funding': 'green-9',
    'Research': 'teal',
    'Innovation': 'deep-orange',
    'Training': 'indigo',
    'Partnership': 'blue-7',
    'News': 'blue',
    'Success Stories': 'orange'
  };

  return categoryColors[category] || 'primary';
}
</script>

<style scoped>
.blog-card {
  height: 100%;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  overflow: hidden;
}

.blog-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Blog image styling */
.blog-image-container {
  position: relative;
  overflow: hidden;
}

.blog-image {
  width: 100%;
  transition: transform 0.3s ease;
}

.blog-image:hover {
  transform: scale(1.02);
}

/* Blog card actions styling */
.blog-card-actions {
  background-color: white;
  position: relative;
  z-index: 2;
  padding-top: 4px;
}

@media (max-width: 599px) {
  .blog-card {
    margin: 0 8px 16px 8px;
    width: calc(100% - 16px);
  }

  /* Adjust action buttons for mobile */
  .blog-card-actions .q-btn {
    padding: 4px 8px;
    min-height: 32px;
  }
}
</style>
