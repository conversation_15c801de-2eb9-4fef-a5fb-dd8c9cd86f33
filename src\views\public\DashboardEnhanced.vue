<template>
  <q-page class="q-pa-md">
    <!-- Loading overlay -->
    <div v-if="isLoading" class="full-page-loader">
      <q-spinner-dots color="primary" size="80px" />
      <div class="q-mt-md text-subtitle1">Loading dashboard...</div>
    </div>

    <!-- Main content - only show when not in loading state -->
    <div v-show="!isLoading">
      <div class="row q-col-gutter-md">
        <!-- Welcome Section with Countdown -->
        <div class="col-12">
          <q-card class="welcome-card bg-light-green-1">
            <q-card-section>
              <div class="row">
                <div class="col-12 col-md-7">
                  <div class="text-h5 text-green-9">Welcome to ZbInnovation</div>
                  <div class="text-subtitle1 q-mt-sm">
                    Welcome back, {{ authStore.currentUser?.email || '<EMAIL>' }}!
                  </div>
                  <div class="text-body1 q-mt-md">
                    <template v-if="!hasAnyProfileData">
                      Create your first profile to get started with ZbInnovation and
                      connect with like-minded innovators!
                    </template>
                    <template v-else-if="!isProfileComplete">
                      Complete your profile to get the most out of ZbInnovation and
                      connect with like-minded innovators!
                    </template>
                    <template v-else>
                      Welcome to your dashboard! Here you can manage your profiles and stay updated
                      with the latest events and opportunities.
                    </template>
                  </div>
                </div>
                <div class="col-12 col-md-5 countdown-column">
                  <div class="countdown-wrapper">
                    <div class="countdown-container" :class="{ 'countdown-mobile': $q.screen.lt.sm }">
                      <div class="text-subtitle1 countdown-label q-mb-sm">Time until launch:</div>
                      <countdown-timer
                        :target-date="targetDate"
                        main-color="#0D8A3E"
                        label-color="#333333"
                        :countdown-size="$q.screen.lt.sm ? '2rem' : '2.5rem'"
                        :label-size="$q.screen.lt.sm ? '0.7rem' : '0.8rem'"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </q-card-section>
          </q-card>
        </div>

        <!-- Profile Type and Status Cards - Only shown for users with profiles -->
        <template v-if="hasAnyProfileData">
          <div class="col-12 col-md-6">
            <q-card class="profile-type-card">
              <q-card-section>
                <div class="row justify-between items-center">
                  <div class="text-h6">Profile Type</div>
                  <q-icon name="lightbulb" size="24px" class="text-grey-8" />
                </div>
                <div class="q-mt-md text-center">
                  <q-badge :color="profileStore.currentProfile?.profile_type ? getProfileTypeColor(profileStore.currentProfile.profile_type) : 'purple'" class="profile-type-badge">
                    <div class="text-subtitle1 q-py-xs q-px-md">{{ profileStore.currentProfile?.profile_type ? formatProfileType(profileStore.currentProfile.profile_type) : 'Innovator' }}</div>
                  </q-badge>
                </div>
              </q-card-section>
            </q-card>
          </div>

          <div class="col-12 col-md-6">
            <q-card class="profile-status-card">
              <q-card-section>
                <div class="row justify-between items-center">
                  <div class="text-h6">Profile Status</div>
                  <q-icon name="verified" size="24px" class="text-grey-8" />
                </div>
                <div class="q-mt-md text-center">
                  <q-badge :color="profileStore.currentProfile?.profile_state ? getProfileStateColor(profileStore.currentProfile.profile_state) : 'green'" class="profile-status-badge">
                    <div class="text-subtitle1 q-py-xs q-px-md">{{ profileStore.currentProfile?.profile_state || 'IN_PROGRESS' }}</div>
                  </q-badge>
                </div>
              </q-card-section>
            </q-card>
          </div>
        </template>

        <!-- Profile Completion Status -->
        <div class="col-12" v-if="profileStore.currentProfile && hasAnyProfileData">
          <profile-completion-status
            :profile="profileStore.currentProfile"
            :compact="true"
          />
        </div>

        <!-- Create Your First Profile - Only shown for users without profiles -->
        <div v-if="!hasAnyProfileData" class="col-12">
          <q-card class="q-pa-lg text-center">
            <q-card-section>
              <unified-icon name="person_add" size="64px" class="text-green-9 q-mb-md" />
              <div class="text-h5 text-green-9">Create Your First Profile</div>
              <p class="text-body1 q-my-md">To get started, create your first profile by selecting your role in the ecosystem.</p>
              <q-btn
                color="green-9"
                label="Create Your Profile"
                :to="{ name: 'profile-create' }"
                size="lg"
                class="q-mt-md"
              >
                <template v-slot:prepend>
                  <unified-icon name="person_add" class="q-mr-xs" />
                </template>
              </q-btn>
            </q-card-section>
          </q-card>
        </div>

        <!-- Upcoming Events -->
        <div class="col-12">
          <q-card class="events-card">
            <q-card-section>
              <div class="text-h6">Upcoming Events</div>
              <div class="q-mt-md">
                <q-list bordered separator class="rounded-borders">
                  <q-item>
                    <q-item-section avatar>
                      <unified-icon name="event" class="text-green-9" />
                    </q-item-section>
                    <q-item-section>
                      <q-item-label>ZbInnovation Launch</q-item-label>
                      <q-item-label caption>April 25th, 2025</q-item-label>
                    </q-item-section>
                    <q-item-section side>
                      <q-badge color="green-9">Coming Soon</q-badge>
                    </q-item-section>
                  </q-item>

                  <q-item>
                    <q-item-section avatar>
                      <unified-icon name="groups" class="text-grey-8" />
                    </q-item-section>
                    <q-item-section>
                      <q-item-label>Early Access Networking Event</q-item-label>
                      <q-item-label caption>Virtual - Date to be announced</q-item-label>
                    </q-item-section>
                    <q-item-section side>
                      <q-badge color="grey-8">Coming Soon</q-badge>
                    </q-item-section>
                  </q-item>

                  <q-item>
                    <q-item-section avatar>
                      <unified-icon name="school" class="text-green-9" />
                    </q-item-section>
                    <q-item-section>
                      <q-item-label>Innovation Workshop Series</q-item-label>
                      <q-item-label caption>Online - Starting Summer 2025</q-item-label>
                    </q-item-section>
                    <q-item-section side>
                      <q-badge color="orange">Planned</q-badge>
                    </q-item-section>
                  </q-item>
                </q-list>
              </div>
            </q-card-section>
          </q-card>
        </div>

        <!-- My Profiles Section -->
        <div class="col-12">
          <q-card>
            <q-card-section>
              <div class="row items-center justify-between q-mb-md">
                <div class="text-h6">My Profiles</div>
                <q-btn
                  v-if="profileStore.userProfiles.filter(p => p.profile_type).length > 0"
                  color="green-9"
                  outline
                  label="Create New Profile"
                  icon="add"
                  :to="{ name: 'profile-create' }"
                />
              </div>
              <div v-if="profileStore.userProfiles.filter(p => p.profile_type).length > 0">
                <q-list bordered separator class="rounded-borders">
                  <q-item
                    v-for="profile in profileStore.userProfiles.filter(p => p.profile_type)"
                    :key="profile.user_id"
                    clickable
                    @click="selectProfile(profile.user_id)"
                    :active="profileStore.currentProfile?.user_id === profile.user_id"
                  >
                    <q-item-section avatar>
                      <q-avatar :color="getProfileTypeColor(profile.profile_type)" text-color="white">
                        {{ (profile.first_name?.[0] || '') + (profile.last_name?.[0] || '') || '?' }}
                      </q-avatar>
                    </q-item-section>
                    <q-item-section>
                      <q-item-label>{{ profile.profile_name || `${profile.first_name || ''} ${profile.last_name || ''}`.trim() || 'Unnamed Profile' }}</q-item-label>
                      <q-item-label caption class="row items-center">
                        <q-badge :color="getProfileTypeColor(profile.profile_type)" text-color="white" class="q-mr-xs">
                          {{ formatProfileType(profile.profile_type) }}
                        </q-badge>
                        <q-badge :color="getProfileStateColor(profile.profile_state)" text-color="white" class="q-mr-xs">
                          {{ profile.profile_state }}
                        </q-badge>
                        <q-linear-progress
                          :value="(profile.profile_completion || 0) / 100"
                          :color="profile.profile_completion >= 100 ? 'positive' : 'primary'"
                          class="q-ml-sm"
                          style="width: 60px; height: 6px;"
                        />
                        <span class="q-ml-xs text-caption" v-if="false">{{ Math.round(profile.profile_completion || 0) }}%</span>
                      </q-item-label>
                    </q-item-section>
                    <q-item-section side>
                      <div class="row q-gutter-sm">
                        <q-btn flat round size="sm" color="green-9" icon="visibility" :to="{ name: 'profile-view', params: { id: profile.user_id } }" />
                        <q-btn flat round size="sm" color="green-9" icon="edit" :to="{ name: 'profile-edit', params: { id: profile.user_id } }" />
                      </div>
                    </q-item-section>
                  </q-item>
                </q-list>
              </div>
              <div v-else class="text-center q-pa-md">
                <q-icon name="person_add" size="3rem" color="grey-5" />
                <p class="text-subtitle1 q-mt-sm">You need to create a profile with a category to get started.</p>
                <q-btn color="green-9" label="Create Your Profile" icon="add" :to="{ name: 'profile-create' }" size="lg" class="q-mt-md" />
              </div>
            </q-card-section>
          </q-card>
        </div>
      </div>
    </div>

    <!-- Profile completion popup -->
    <q-dialog v-model="showProfileCompletionPopup" persistent>
      <profile-completion-popup
        v-model="showProfileCompletionPopup"
        :is-initial="isNewUser"
        @remind-later="handleRemindLater"
      />
    </q-dialog>

    <!-- Profile creation modal -->
    <profile-creation-modal
      v-model="showProfileCreationModal"
      @profile-created="handleProfileCreated"
    />
  </q-page>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useAuthStore } from '../stores/auth'
import { useProfileStore } from '../stores/profile'
import UnifiedIcon from '../components/ui/UnifiedIcon.vue'
import ProfileCompletionPopup from '../components/profile/ProfileCompletionPopup.vue'
import ProfileCreationModal from '../components/profile/ProfileCreationModal.vue'
import ProfileCompletionStatus from '../components/profile/ProfileCompletionStatus.vue'
import CountdownTimer from '../components/CountdownTimer.vue'
import { formatProfileType, getProfileTypeIcon } from '../services/profileTypes'

// Import simplified services
import { useUserState } from '../services/userStateService'
import { useGlobalServicesStore } from '../stores/globalServices'

const authStore = useAuthStore()
const profileStore = useProfileStore()
const globalServices = useGlobalServicesStore()

// Use simplified services
const { isNewUser, hasIncompleteProfile, hasAnyProfileData, isLoading: userStateLoading, checkUserState, profileData } = useUserState()
const { isProfileComplete } = globalServices.profileCompletionService

// Simple loading state
const isLoading = ref(true)

// UI state
const showProfileCompletionPopup = ref(false)
const showProfileCreationModal = ref(false)

// Countdown target date - April 25th, 2025
const targetDate = '2025-05-14T00:00:00Z'

// Lifecycle hooks
onMounted(async () => {
  isLoading.value = true
  try {
    // Direct check of user state - single database query
    await checkUserState()

    // Load minimal profile data if needed
    if (hasAnyProfileData.value && !profileStore.currentProfile) {
      await profileStore.loadUserProfiles()
    }
  } catch (error) {
    console.error('Error loading dashboard data:', error)
  } finally {
    isLoading.value = false
  }
})

// Computed
const welcomeMessage = computed(() => {
  if (authStore.currentUser?.email) {
    return `Welcome back, ${authStore.currentUser.email}!`
  }
  return 'Welcome to your dashboard!'
})

// Methods
function getProfileStateColor(state: string | undefined): string {
  if (!state) return 'grey'

  const colors: Record<string, string> = {
    'DRAFT': 'grey',
    'IN_PROGRESS': 'blue',
    'PENDING_APPROVAL': 'orange',
    'ACTIVE': 'green',
    'DISABLED': 'red',
    'DECLINED': 'red-6'
  }

  return colors[state] || 'grey'
}

function getProfileTypeColor(type: string | null | undefined): string {
  if (!type) return 'grey'

  const colors: Record<string, string> = {
    'innovator': 'purple',
    'investor': 'green',
    'mentor': 'blue',
    'professional': 'teal',
    'industry_expert': 'deep-orange',
    'academic_student': 'indigo',
    'academic_institution': 'light-blue',
    'organisation': 'amber'
  }

  return colors[type] || 'grey'
}

function selectProfile(profileId: string): void {
  profileStore.setCurrentProfile(profileId)
}

// Handle profile creation events
function handleProfileCreated(profile) {
  console.log('Profile created:', profile)
  // Force reload profiles to update the UI
  profileStore.loadUserProfiles()
}

// Handle loading state changes from profile creation
function handleProfileCreationLoadingChange(isLoading) {
  // This could be used to show a loading indicator if needed
  console.log('Profile creation loading state changed:', isLoading)
}

// Handle remind later for profile completion popup
function handleRemindLater() {
  // Store the current time in localStorage
  localStorage.setItem('profileCompletionRemindLater', Date.now().toString())
  showProfileCompletionPopup.value = false
}

// No initialization code needed here - everything is handled in onMounted
</script>

<style scoped>
/* Loading overlay */
.full-page-loader {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

.welcome-card {
  background-color: #f0f8f1;
  color: #0D8A3E;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Countdown column and wrapper */
.countdown-column {
  display: flex;
  align-items: center;
}

.countdown-wrapper {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

/* Countdown container with label on top */
.countdown-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  width: 100%;
}

.countdown-label {
  white-space: nowrap;
  font-weight: 500;
  color: #0D8A3E;
}

/* Mobile-specific countdown styles */
.countdown-mobile {
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
}

/* Ensure the countdown fits on small screens */
@media (max-width: 599px) {
  .countdown-wrapper {
    margin-top: 1rem;
  }

  :deep(.flip-countdown) {
    gap: 0.25rem;
  }

  :deep(.flip-countdown .flip-card) {
    padding: 0.25rem;
  }
}

/* Custom styles for countdown timer */
:deep(.flip-countdown) {
  display: flex !important;
  justify-content: flex-start !important;
  align-items: center !important;
  gap: 0.5rem !important;
  flex-wrap: nowrap !important;
  flex-direction: row !important;
  width: 100% !important;
}

:deep(.flip-countdown .flip-card) {
  background: white !important;
  border-radius: 8px !important;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05) !important;
  padding: 0.5rem !important;
  flex: 0 0 auto !important;
}

:deep(.flip-countdown .flip-card:nth-child(odd)) {
  background: #0D8A3E !important;
}

:deep(.flip-countdown .flip-card:nth-child(even)) {
  background: #333333 !important;
}

/* Profile badges */
.profile-type-badge, .profile-status-badge {
  border-radius: 20px;
  padding: 4px 8px;
}

.profile-type-card, .profile-status-card, .profile-completion-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: 100%;
}

.profile-completion-card {
  border-left: 4px solid #0D8A3E; /* Green for completion */
}

.profile-type-card {
  border-left: 4px solid var(--q-primary); /* Dynamic color based on profile type */
}

.profile-status-card {
  border-left: 4px solid var(--q-secondary); /* Dynamic color based on profile status */
}

.events-card {
  margin-top: 1rem;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* Progress bar */
.q-linear-progress {
  height: 10px;
  border-radius: 5px;
  overflow: hidden;
}

/* Card styling */
.q-card {
  transition: all 0.2s ease;
  border-radius: 8px;
}

.q-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
</style>
