<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useNewsStore } from '../../stores/news';


const router = useRouter();
const newsStore = useNewsStore();
const newsItems = ref([]);
const featuredNews = ref([]);
const loading = ref(true);
const error = ref(null);
const showFeatured = ref(true); // Default to showing featured news

// Fetch news on component mount
onMounted(async () => {
  try {
    loading.value = true;

    await newsStore.fetchNews();

    // Get all news items
    const allNews = newsStore.newsItems;

    // Filter featured news (articles with 'featured' tag or category)
    featuredNews.value = allNews.filter(item => {
      return (
        (item.tags && Array.isArray(item.tags) && item.tags.includes('featured')) ||
        item.category?.toLowerCase() === 'featured' ||
        item.subType?.toLowerCase() === 'featured' ||
        item.isFeatured === true
      );
    }).slice(0, 3); // Only show latest 3 featured items

    // Always show featured news if available, otherwise show latest 3 news items
    if (featuredNews.value.length === 0) {
      showFeatured.value = false;
      newsItems.value = allNews.slice(0, 3); // Show latest 3 if no featured content
    } else {
      showFeatured.value = true;
      newsItems.value = featuredNews.value;
    }
  } catch (err) {
    console.error('Error loading news:', err);
    error.value = 'Failed to load news';
  } finally {
    loading.value = false;
  }
});

const navigateToStory = (id: number) => {
  router.push(`/news/${id}`);
};


</script>

<template>
  <section id="news-section" class="news-section q-py-xl">
    <div class="container q-mx-auto q-px-md">
      <div class="row">
        <div class="col-1" />
        <div class="col-10">
          <h2 class="text-h3 text-weight-light q-mb-md text-center">News & Updates</h2>

          <!-- Show featured badge if we have featured content -->
          <div v-if="featuredNews.length > 0" class="text-center q-mb-lg">
            <q-chip color="primary" text-color="white" icon="star" class="featured-badge">
              Featured News & Updates
            </q-chip>
          </div>

          <!-- Loading state -->
          <div v-if="loading" class="row justify-center q-py-md">
            <q-spinner color="primary" size="3em" />
          </div>

          <!-- Error state -->
          <div v-else-if="error" class="row justify-center q-py-md">
            <div class="text-center">
              <p class="text-negative">{{ error }}</p>
              <q-btn color="primary" label="Retry" @click="newsStore.fetchNews()" />
            </div>
          </div>

          <!-- Content -->
          <div v-else class="row q-col-gutter-lg justify-center">
            <div v-for="item in newsItems.slice(0, 3)" :key="item.id" class="col-md-4 col-sm-12">
              <article
                class="news-card q-pa-md relative-position"
                :class="{'featured': (item.tags && item.tags.includes('featured')) || item.category?.toLowerCase() === 'featured'}"
                :aria-labelledby="'news-title-' + item.id"
              >
                <header>
                  <q-chip
                    :color="item.categoryColor"
                    :text-color="item.textColor"
                    class="q-mb-sm"
                    size="sm"
                  >
                    {{ item.category }}
                  </q-chip>
                  <h3 :id="'news-title-' + item.id" class="text-h6 q-mb-sm">{{ item.title }}</h3>
                  <time :datetime="item.date" class="text-caption text-grey">
                    {{ new Date(item.date).toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' }) }}
                  </time>
                </header>
                <p class="q-mt-md q-mb-lg text-body2">{{ item.excerpt }}</p>
                <div class="row items-center justify-end">
                  <q-btn
                    flat
                    color="primary"
                    :to="{ name: 'article', params: { slug: item.slug }}"
                    label="Read More"
                    no-caps
                    class="q-px-md"
                    :aria-label="'Read more about ' + item.title"
                  >
                    <q-tooltip>View full article</q-tooltip>
                  </q-btn>
                </div>
              </article>
            </div>
          </div>

          <!-- View All News Link -->
          <div class="text-center q-mt-xl">
            <q-btn
              color="primary"
              :to="'/virtual-community?tab=blog'"
              label="View All News"
              no-caps
              class="q-px-xl cta-button"
              unelevated
              rounded
              size="md"
            >
              <q-tooltip>Browse all news and updates</q-tooltip>
            </q-btn>
          </div>
        </div>
        <div class="col-1" />
      </div>
    </div>
  </section>
</template>

<style scoped>
.news-section {
  background-color: #f5f5f5;
  padding: 70px 0;
}

.container {
  width: 100%;
  max-width: 1400px;
}

.news-card {
  border-radius: 12px;
  transition: all 0.3s ease;
  height: 100%;
  background: white;
}

.news-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(0,0,0,0.1);
}

.cta-button {
  border-radius: 25px !important;
  min-width: 160px;
  height: 40px;
  font-weight: 600;
}

.featured-badge {
  margin-bottom: 20px;
  font-weight: 600;
  padding: 8px 16px;
}

/* Add a subtle badge for featured articles */
.news-card.featured::before {
  content: 'Featured';
  position: absolute;
  top: 10px;
  right: 10px;
  background: #0D8A3E;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: bold;
  z-index: 1;
}

.category-chip {
  font-size: 0.875rem;
  font-weight: 500;
  padding: 4px 12px;
  border-radius: 20px;
}

.news-title {
  font-weight: 600;
  line-height: 1.4;
  font-size: 1.5rem;
}

.read-more-btn {
  font-weight: 500;
  text-transform: none;
  padding: 0;
}

.read-more-btn:hover {
  opacity: 0.9;
}

.author-info {
  display: flex;
  flex-direction: column;
}

@media (max-width: 767px) {
  .news-section {
    padding: 40px 0;
  }

  .news-card {
    margin-bottom: 1rem;
  }

  .news-title {
    font-size: 1.25rem;
  }

  .q-mb-xl {
    margin-bottom: 1rem !important;
  }

  .q-col-gutter-lg {
    margin: -4px !important;
  }

  .q-col-gutter-lg > * {
    padding: 4px !important;
  }
}
</style>

