<template>
  <div class="activity-feed">
    <div v-if="title" class="text-h6 q-mb-md">{{ title }}</div>

    <div v-if="loading" class="text-center q-pa-md">
      <q-spinner color="primary" size="2em" />
      <p>Loading activities...</p>
    </div>

    <div v-else-if="activities.length === 0" class="text-center q-pa-md text-grey">
      <q-icon name="info" size="2em" />
      <p>No activities to display</p>
    </div>

    <div v-else>
      <q-timeline color="primary">
        <q-timeline-entry
          v-for="activity in activities"
          :key="activity.id"
          :title="getActivityTitle(activity)"
          :subtitle="formatDate(activity.timestamp)"
          :icon="getActivityIcon(activity)"
          :color="getActivityColor(activity)"
        >
          <div>{{ getActivityDescription(activity) }}</div>

          <div v-if="activity.details && activity.details.post_id" class="q-mt-sm">
            <q-btn
              flat
              dense
              size="sm"
              color="primary"
              :to="getPostViewRoute(activity)"
              label="View Post"
            />

            <!-- Edit and Delete buttons for user's own posts -->
            <template v-if="activity.activity_type === 'post_create' && isCurrentUserActivity(activity)">
              <q-btn
                flat
                dense
                size="sm"
                color="secondary"
                icon="edit"
                label="Edit"
                @click="openEditDialog(activity)"
              />
              <q-btn
                flat
                dense
                size="sm"
                color="negative"
                icon="delete"
                label="Delete"
                @click="confirmDelete(activity)"
              />
            </template>
          </div>
        </q-timeline-entry>
      </q-timeline>

      <div v-if="hasMore" class="text-center q-mt-md">
        <q-btn
          outline
          color="primary"
          label="Load More"
          :loading="loadingMore"
          @click="loadMore"
        />
      </div>
    </div>
  </div>

  <!-- Edit Post Dialog -->
  <q-dialog v-model="showEditDialog" persistent>
    <q-card style="min-width: 350px">
      <q-card-section>
        <div class="text-h6">Edit Post</div>
      </q-card-section>

      <q-card-section>
        <q-input
          v-model="editForm.title"
          label="Title"
          outlined
          :rules="[val => !!val || 'Title is required']"
        />

        <q-input
          v-model="editForm.content"
          label="Content"
          type="textarea"
          outlined
          class="q-mt-md"
          :rules="[val => !!val || 'Content is required']"
          autogrow
        />
      </q-card-section>

      <q-card-actions align="right">
        <q-btn flat label="Cancel" color="primary" v-close-popup />
        <q-btn
          flat
          label="Save"
          color="primary"
          @click="savePostEdit"
          :loading="saving"
          :disable="!editForm.title || !editForm.content"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>

  <!-- Delete Confirmation Dialog -->
  <q-dialog v-model="showDeleteDialog" persistent>
    <q-card>
      <q-card-section class="row items-center">
        <q-avatar icon="warning" color="negative" text-color="white" />
        <span class="q-ml-sm text-h6">Delete Post</span>
      </q-card-section>

      <q-card-section>
        Are you sure you want to delete this post? This action cannot be undone.
      </q-card-section>

      <q-card-actions align="right">
        <q-btn flat label="Cancel" color="primary" v-close-popup />
        <q-btn flat label="Delete" color="negative" @click="deletePost" :loading="deleting" />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { date, useQuasar } from 'quasar';
import { useGlobalServicesStore } from '../../stores/globalServices';
import { usePostsStore } from '../../stores/posts';
import { useAuthStore } from '../../stores/auth';
import { getNameFromEmail } from '../../utils/nameUtils';

const props = defineProps({
  userId: {
    type: String,
    default: ''
  },
  title: {
    type: String,
    default: 'Recent Activity'
  },
  limit: {
    type: Number,
    default: 5
  }
});

const $q = useQuasar();
const globalServices = useGlobalServicesStore();
const activityService = globalServices.activityService;
const postsStore = usePostsStore();
const authStore = useAuthStore();

// Check if the current user is the one who created the activity
const currentUserId = computed(() => authStore.currentUser?.id || '');

function isCurrentUserActivity(activity) {
  // If we're viewing our own dashboard, all activities are ours
  if (props.userId && props.userId === currentUserId.value) {
    return true;
  }

  // Otherwise, check if the user_id of the activity matches the current user
  return activity.user_id === currentUserId.value;
}

// Activity feed state
const activities = ref([]);
const loading = ref(true);
const loadingMore = ref(false);
const currentPage = ref(1);
const hasMore = ref(false);

// Edit dialog state
const showEditDialog = ref(false);
const editForm = ref({
  id: null,
  title: '',
  content: ''
});
const saving = ref(false);
const currentActivity = ref(null);

// Delete dialog state
const showDeleteDialog = ref(false);
const deleting = ref(false);

onMounted(async () => {
  await loadActivities();
});

async function loadActivities() {
  try {
    loading.value = true;
    const result = await activityService.getUserActivities(
      props.userId,
      props.limit,
      currentPage.value
    );

    activities.value = result;
    hasMore.value = result.length === props.limit;
  } catch (error) {
    console.error('Error loading activities:', error);
  } finally {
    loading.value = false;
  }
}

async function loadMore() {
  try {
    loadingMore.value = true;
    currentPage.value++;

    const result = await activityService.getUserActivities(
      props.userId,
      props.limit,
      currentPage.value
    );

    activities.value = [...activities.value, ...result];
    hasMore.value = result.length === props.limit;
  } catch (error) {
    console.error('Error loading more activities:', error);
    currentPage.value = Math.max(1, currentPage.value - 1);
  } finally {
    loadingMore.value = false;
  }
}

function getActivityTitle(activity) {
  const activityTitles = {
    'post_create': 'Created a post',
    'post_like': 'Liked a post',
    'post_comment': 'Commented on a post',
    'connect_request': 'Sent a connection request',
    'connect_accept': 'Accepted a connection request',
    'profile_update': 'Updated profile',
    'group_join': 'Joined a group',
    'event_register': 'Registered for an event',
    'success_story_submit': 'Shared a success story',
    'content_save': 'Saved content',
    'content_unsave': 'Removed saved content'
  };

  return activityTitles[activity.activity_type] || 'Activity';
}

function getActivityIcon(activity) {
  const activityIcons = {
    'post_create': 'post_add',
    'post_like': 'favorite',
    'post_comment': 'comment',
    'connect_request': 'person_add',
    'connect_accept': 'handshake',
    'profile_update': 'account_circle',
    'group_join': 'group_add',
    'event_register': 'event',
    'success_story_submit': 'emoji_events',
    'content_save': 'bookmark',
    'content_unsave': 'bookmark_border'
  };

  return activityIcons[activity.activity_type] || 'info';
}

function getActivityColor(activity) {
  const activityColors = {
    'post_create': 'green',
    'post_like': 'pink',
    'post_comment': 'blue',
    'connect_request': 'purple',
    'connect_accept': 'deep-purple',
    'profile_update': 'teal',
    'group_join': 'indigo',
    'event_register': 'orange',
    'success_story_submit': 'amber',
    'content_save': 'cyan',
    'content_unsave': 'grey'
  };

  return activityColors[activity.activity_type] || 'primary';
}

function getActivityDescription(activity) {
  const details = activity.details || {};

  switch (activity.activity_type) {
    case 'post_create':
      return details.title
        ? `Created a post: "${details.title}"`
        : `Created a new post`;
    case 'post_like':
      // If we have an author email but no name, generate a name from the email
      if (!details.author_name && details.author_email) {
        details.author_name = getNameFromEmail(details.author_email);
      }

      return details.post_title
        ? `Liked a post: "${details.post_title}"`
        : `Liked a post by ${details.author_name || 'someone'}`;
    case 'post_comment':
      return details.comment
        ? `Commented: "${details.comment}"`
        : `Commented on a post`;
    case 'connect_request':
      return `Sent a connection request`;
    case 'connect_accept':
      return `Accepted a connection request`;
    case 'profile_update':
      return `Updated profile information`;
    case 'group_join':
      return details.group_name
        ? `Joined the group "${details.group_name}"`
        : `Joined a group`;
    case 'event_register':
      return details.event_title
        ? `Registered for event: "${details.event_title}"`
        : `Registered for an event`;
    case 'success_story_submit':
      return details.title
        ? `Shared a success story: "${details.title}"`
        : `Shared a success story`;
    case 'content_save':
      return details.content_title
        ? `Saved ${details.content_type}: "${details.content_title}"`
        : `Saved ${details.content_type || 'content'}`;
    case 'content_unsave':
      return details.content_title
        ? `Removed ${details.content_type}: "${details.content_title}"`
        : `Removed saved ${details.content_type || 'content'}`;
    default:
      return 'Performed an activity';
  }
}

// Open edit dialog for a post
async function openEditDialog(activity) {
  currentActivity.value = activity;
  const postId = activity.details.post_id;

  try {
    // Fetch the post details
    const post = await postsStore.getPostById(postId);

    if (!post) {
      $q.notify({
        color: 'negative',
        message: 'Post not found',
        position: 'top'
      });
      return;
    }

    // Populate the edit form
    editForm.value = {
      id: post.id,
      title: post.title || '',
      content: post.content || ''
    };

    // Show the dialog
    showEditDialog.value = true;
  } catch (error) {
    console.error('Error fetching post for editing:', error);
    $q.notify({
      color: 'negative',
      message: 'Failed to load post details',
      position: 'top'
    });
  }
}

// Save edited post
async function savePostEdit() {
  if (!editForm.value.title || !editForm.value.content) {
    return;
  }

  saving.value = true;

  try {
    // Update the post
    const success = await postsStore.updatePost({
      id: editForm.value.id,
      title: editForm.value.title,
      content: editForm.value.content
    });

    if (success) {
      // Update the activity details
      if (currentActivity.value && currentActivity.value.details) {
        currentActivity.value.details.title = editForm.value.title;
      }

      // Close the dialog
      showEditDialog.value = false;

      // Show success notification
      $q.notify({
        color: 'positive',
        message: 'Post updated successfully',
        position: 'top'
      });
    } else {
      throw new Error('Failed to update post');
    }
  } catch (error) {
    console.error('Error updating post:', error);
    $q.notify({
      color: 'negative',
      message: error.message || 'Failed to update post',
      position: 'top'
    });
  } finally {
    saving.value = false;
  }
}

// Confirm post deletion
function confirmDelete(activity) {
  currentActivity.value = activity;
  showDeleteDialog.value = true;
}

// Delete post
async function deletePost() {
  if (!currentActivity.value || !currentActivity.value.details) {
    return;
  }

  const postId = currentActivity.value.details.post_id;
  deleting.value = true;

  try {
    // Delete the post
    const success = await postsStore.deletePost(postId);

    if (success) {
      // Remove the activity from the list
      activities.value = activities.value.filter(a =>
        !(a.activity_type === 'post_create' && a.details && a.details.post_id === postId)
      );

      // Close the dialog
      showDeleteDialog.value = false;

      // Show success notification
      $q.notify({
        color: 'positive',
        message: 'Post deleted successfully',
        position: 'top'
      });
    } else {
      throw new Error('Failed to delete post');
    }
  } catch (error) {
    console.error('Error deleting post:', error);
    $q.notify({
      color: 'negative',
      message: error.message || 'Failed to delete post',
      position: 'top'
    });
  } finally {
    deleting.value = false;
  }
}

// Determine the correct route for viewing a post
function getPostViewRoute(activity) {
  if (!activity.details || !activity.details.post_id) {
    return { name: 'virtual-community' };
  }

  const postId = activity.details.post_id;
  const postType = activity.details.post_type || '';

  // Determine the route based on post type
  switch(postType.toLowerCase()) {
    case 'blog':
      return { name: 'article', params: { slug: activity.details.slug || postId } };
    case 'event':
      return { name: 'event-details', params: { id: postId } };
    case 'marketplace':
      return { name: 'marketplace-listing', params: { id: postId } };
    case 'success_story':
      return {
        name: 'post-details',
        params: { id: postId },
        query: { type: 'success-story' }
      };
    default:
      // For general posts, use the post-details route
      return { name: 'post-details', params: { id: postId } };
  }
}

function formatDate(dateString) {
  const now = new Date();
  const activityDate = new Date(dateString);
  const diffDays = Math.floor((now - activityDate) / (1000 * 60 * 60 * 24));

  if (diffDays === 0) {
    return 'Today at ' + date.formatDate(dateString, 'h:mm A');
  } else if (diffDays === 1) {
    return 'Yesterday at ' + date.formatDate(dateString, 'h:mm A');
  } else if (diffDays < 7) {
    return date.formatDate(dateString, 'dddd [at] h:mm A');
  } else {
    return date.formatDate(dateString, 'MMM D, YYYY [at] h:mm A');
  }
}
</script>

<style scoped>
.activity-feed {
  background-color: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
}

@media (max-width: 599px) {
  .activity-feed {
    padding: 12px;
  }
}
</style>
