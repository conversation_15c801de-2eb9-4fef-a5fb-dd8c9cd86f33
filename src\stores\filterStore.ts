import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { usePostsStore } from './posts';

export const useFilterStore = defineStore('filters', () => {
  // Use the posts store
  const postsStore = usePostsStore();

  // Base filter state
  const activeTab = ref('feed');
  const searchQuery = ref('');
  const dateRange = ref('all');

  // Tab-specific filter states
  const feedFilters = ref({
    postTypes: [],
    categories: [],
    opportunityTypes: []
  });

  const profileFilters = ref({
    profileTypes: []
  });

  const blogFilters = ref({
    blogCategories: [],
    readTime: 'any'
  });

  const eventFilters = ref({
    eventTypes: [],
    eventFormat: [],
    eventDate: null
  });

  const groupFilters = ref({
    groupCategories: [],
    memberRange: { min: 0, max: 1000 }
  });

  const marketplaceFilters = ref({
    listingTypes: [],
    priceRange: { min: 0, max: 10000 }
  });

  // Computed property to get current tab's filters
  const currentFilters = computed(() => {
    switch (activeTab.value) {
      case 'feed':
        return {
          searchQuery: searchQuery.value,
          dateRange: dateRange.value,
          ...feedFilters.value
        };
      case 'profiles':
        return {
          searchQuery: searchQuery.value,
          dateRange: dateRange.value,
          ...profileFilters.value
        };
      case 'blog':
        return {
          searchQuery: searchQuery.value,
          dateRange: dateRange.value,
          ...blogFilters.value
        };
      case 'events':
        return {
          searchQuery: searchQuery.value,
          dateRange: dateRange.value,
          ...eventFilters.value
        };
      case 'groups':
        return {
          searchQuery: searchQuery.value,
          dateRange: dateRange.value,
          ...groupFilters.value
        };
      case 'marketplace':
        return {
          searchQuery: searchQuery.value,
          dateRange: dateRange.value,
          ...marketplaceFilters.value
        };
      default:
        return {
          searchQuery: searchQuery.value,
          dateRange: dateRange.value
        };
    }
  });

  // Convert filter store format to posts store format
  const postsFilter = computed(() => {
    const filter: any = {
      searchQuery: searchQuery.value,
      dateRange: dateRange.value,
      postTypes: [],
      subTypes: []
    };

    // Add post types based on active tab
    switch (activeTab.value) {
      case 'feed':
        if (feedFilters.value.postTypes && feedFilters.value.postTypes.length > 0) {
          filter.postTypes = feedFilters.value.postTypes;
        }
        if (feedFilters.value.categories && feedFilters.value.categories.length > 0) {
          filter.subTypes = feedFilters.value.categories;
        }
        break;
      case 'blog':
        filter.postTypes = ['BLOG'];
        if (blogFilters.value.blogCategories && blogFilters.value.blogCategories.length > 0) {
          filter.subTypes = blogFilters.value.blogCategories;
        }
        break;
      case 'events':
        filter.postTypes = ['EVENT'];
        if (eventFilters.value.eventTypes && eventFilters.value.eventTypes.length > 0) {
          filter.subTypes = eventFilters.value.eventTypes;
        }
        break;
    }

    return filter;
  });

  // Removed debounced function since we're not auto-fetching data anymore

  // REMOVED: All automatic watchers to prevent infinite loops
  // Components will handle data fetching manually when needed

  // Actions
  function setActiveTab(tab, fetchData = false) {
    // Simple tab update without automatic data fetching
    if (activeTab.value !== tab) {
      console.log(`Filter store: Setting active tab to ${tab}`);
      activeTab.value = tab;

      // Only fetch data if explicitly requested (which we won't do to prevent loops)
      if (fetchData) {
        console.log('Data fetching disabled in filter store to prevent infinite loops');
      }
    }
  }

  function setSearchQuery(query) {
    searchQuery.value = query;
  }

  function setDateRange(range) {
    dateRange.value = range;
  }

  function updateFeedFilters(filters) {
    feedFilters.value = { ...feedFilters.value, ...filters };

    // Only update the filter, don't fetch data automatically
    // The component will handle fetching when needed
    postsStore.setFilter(postsFilter.value);
  }

  function updateProfileFilters(filters) {
    profileFilters.value = { ...profileFilters.value, ...filters };
  }

  function updateBlogFilters(filters) {
    blogFilters.value = { ...blogFilters.value, ...filters };

    // Only update the filter, don't fetch data automatically
    // The component will handle fetching when needed
    postsStore.setFilter(postsFilter.value);
  }

  function updateEventFilters(filters) {
    eventFilters.value = { ...eventFilters.value, ...filters };

    // Only update the filter, don't fetch data automatically
    // The component will handle fetching when needed
    postsStore.setFilter(postsFilter.value);
  }

  function updateGroupFilters(filters) {
    groupFilters.value = { ...groupFilters.value, ...filters };
  }

  function updateMarketplaceFilters(filters) {
    marketplaceFilters.value = { ...marketplaceFilters.value, ...filters };
  }

  function resetAllFilters() {
    searchQuery.value = '';
    dateRange.value = 'all';
    feedFilters.value = { postTypes: [], categories: [], opportunityTypes: [] };
    profileFilters.value = { profileTypes: [] };
    blogFilters.value = { blogCategories: [], readTime: 'any' };
    eventFilters.value = { eventTypes: [], eventFormat: [], eventDate: null };
    groupFilters.value = { groupCategories: [], memberRange: { min: 0, max: 1000 } };
    marketplaceFilters.value = { listingTypes: [], priceRange: { min: 0, max: 10000 } };

    // Reset posts store filter
    postsStore.resetFilter();
  }

  function resetCurrentTabFilters() {
    searchQuery.value = '';
    dateRange.value = 'all';

    switch (activeTab.value) {
      case 'feed':
        feedFilters.value = { postTypes: [], categories: [], opportunityTypes: [] };
        break;
      case 'profiles':
        profileFilters.value = { profileTypes: [] };
        break;
      case 'blog':
        blogFilters.value = { blogCategories: [], readTime: 'any' };
        break;
      case 'events':
        eventFilters.value = { eventTypes: [], eventFormat: [], eventDate: null };
        break;
      case 'groups':
        groupFilters.value = { groupCategories: [], memberRange: { min: 0, max: 1000 } };
        break;
      case 'marketplace':
        marketplaceFilters.value = { listingTypes: [], priceRange: { min: 0, max: 10000 } };
        break;
    }

    // Reset posts store filter
    postsStore.resetFilter();

    // Don't automatically fetch data - let the component handle it
    // This prevents infinite loops and unnecessary database calls
  }

  return {
    // State
    activeTab,
    searchQuery,
    dateRange,
    feedFilters,
    profileFilters,
    blogFilters,
    eventFilters,
    groupFilters,
    marketplaceFilters,

    // Computed
    currentFilters,
    postsFilter,

    // Actions
    setActiveTab,
    setSearchQuery,
    setDateRange,
    updateFeedFilters,
    updateProfileFilters,
    updateBlogFilters,
    updateEventFilters,
    updateGroupFilters,
    updateMarketplaceFilters,
    resetAllFilters,
    resetCurrentTabFilters
  };
});
